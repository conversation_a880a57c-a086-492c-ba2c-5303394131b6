// ==================== KIT & TUTORIAL MANAGEMENT SYSTEM ========================
// Functions for managing kits, tutorials, and redemption codes
// Created: 2025-08-01

// ==================== KIT MANAGEMENT ========================

// Kit-Tutorial Mapping for premium access
const kitTutorialMapping = {
    'robot-nolep': [
        { id: 'dht11-basics', title: 'Video 1: Baca Suhu DHT11', duration: '15 menit' },
        { id: 'oled-display', title: 'Video 2: Tam<PERSON><PERSON>an ke OLED', duration: '22 menit' },
        { id: 'wifi-control', title: 'Video 3: Kontrol via WiFi', duration: '28 menit' }
    ],
    'weather-station': [
        { id: 'weather-sensors', title: 'Weather Sensors Setup', duration: '20 menit' },
        { id: 'data-logging', title: 'Data Logging System', duration: '25 menit' }
    ],
    'iot-security': [
        { id: 'security-basics', title: 'Security Basics', duration: '18 menit' },
        { id: 'camera-integration', title: 'Camera Integration', duration: '30 menit' },
        { id: 'motion-detection', title: 'Motion Detection', duration: '25 menit' }
    ],
    'disco-light': [
        { id: 'led-programming', title: 'LED Strip Programming', duration: '20 menit' },
        { id: 'audio-processing', title: 'Audio Processing', duration: '25 menit' }
    ],
    'smart-garden': [
        { id: 'sensor-automation', title: 'Sensor Automation', duration: '22 menit' },
        { id: 'plant-care-system', title: 'Plant Care System', duration: '28 menit' }
    ],
    'led-matrix': [
        { id: 'matrix-programming', title: 'Matrix Programming', duration: '25 menit' },
        { id: 'graphics-rendering', title: 'Graphics Rendering', duration: '30 menit' }
    ]
};

// Buy Kit Function - Redirect to Tokopedia
const buyKit = (kitId) => {
    const kitUrls = {
        'robot-nolep': 'https://tokopedia.com/techno-rebel/robot-nolep-kit',
        'weather-station': 'https://tokopedia.com/techno-rebel/weather-station-kit',
        'disco-light': 'https://tokopedia.com/techno-rebel/disco-light-kit',
        'iot-security': 'https://tokopedia.com/techno-rebel/iot-security-kit',
        'smart-garden': 'https://tokopedia.com/techno-rebel/smart-garden-kit',
        'led-matrix': 'https://tokopedia.com/techno-rebel/led-matrix-kit'
    };
    
    const url = kitUrls[kitId] || 'https://tokopedia.com/techno-rebel';
    window.open(url, '_blank');
};

// Buy Premium Tutorial via WhatsApp
const buyPremiumTutorial = (tutorialId) => {
    const message = `Halo! Saya ingin membeli akses tutorial premium: ${tutorialId}. Mohon info harga dan cara pembayarannya. Terima kasih!`;
    const whatsappUrl = `https://wa.me/6281234567890?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
};

// Get kit information by ID
const getKitInfo = (kitId) => {
    const kitInfo = {
        'robot-nolep': {
            name: 'Robot Nolep Kit',
            price: 350000,
            description: 'Rakit robot yang jago ghosting tembok dan rintangan lain.',
            difficulty: 'beginner',
            components: ['ESP32 Trainer Board', 'OLED Display', 'DHT11 Sensor', 'Push Buttons'],
            tutorials: kitTutorialMapping['robot-nolep']
        },
        'weather-station': {
            name: 'Weather Station Kit',
            price: 425000,
            description: 'Bikin stasiun cuaca pribadi yang bisa monitoring suhu, kelembaban, tekanan udara.',
            difficulty: 'intermediate',
            components: ['ESP32 DevKit', 'BME280 Sensor', 'OLED Display', 'RTC Module'],
            tutorials: kitTutorialMapping['weather-station']
        },
        'iot-security': {
            name: 'IoT Security Kit',
            price: 650000,
            description: 'Sistem keamanan rumah dengan kamera, sensor gerak, dan notifikasi real-time.',
            difficulty: 'advanced',
            components: ['ESP32-CAM', 'PIR Sensor', 'Door Sensor', 'Buzzer'],
            tutorials: kitTutorialMapping['iot-security']
        }
    };
    
    return kitInfo[kitId] || null;
};

// ==================== REDEMPTION CODE SYSTEM ========================

// Redeem Kit Code Function
const redeemKitCode = async (event) => {
    event.preventDefault();
    const codeInput = document.getElementById('kit-code-input');
    const code = codeInput.value.trim().toUpperCase();
    
    if (!code) {
        showCodeResult('error', 'Masukkan kode terlebih dahulu!');
        return;
    }

    // Validate code format (KIT-NAME-CODE)
    const codePattern = /^[A-Z]+-[A-Z]+-[A-Z0-9]+$/;
    if (!codePattern.test(code)) {
        showCodeResult('error', 'Format kode salah! Gunakan format: NAMA-KIT-KODE');
        return;
    }

    // Show loading
    showCodeResult('loading', 'Memvalidasi kode...');

    try {
        // Get current user
        const userResult = await getCurrentUser();
        if (!userResult.success || !userResult.user) {
            showCodeResult('error', 'Silakan login terlebih dahulu!');
            return;
        }

        // Use Supabase function if available, otherwise fallback to mock
        let result;
        if (typeof redeemKitCodeSupabase === 'function') {
            result = await redeemKitCodeSupabase(code, userResult.user.id);
        } else {
            // Fallback to mock function for development
            result = await validateKitCode(code);
            if (result.success) {
                await updateUserPremiumAccess(result.kitId, result.tutorials);
            }
        }
        
        if (result.success) {
            showCodeResult('success', `Berhasil! Anda mendapat akses ke ${result.tutorials.length} tutorial premium dari ${result.kitName}.`);
            
            setTimeout(() => {
                closeModal('activation-modal');
                showPage('member-area-page');
                // Refresh dashboard to show new content
                location.reload();
            }, 2000);
        } else {
            showCodeResult('error', result.message || 'Kode tidak valid atau sudah digunakan!');
        }
    } catch (error) {
        console.error('Code redemption error:', error);
        showCodeResult('error', 'Terjadi kesalahan. Silakan coba lagi.');
    }
};

// Show code validation result
const showCodeResult = (type, message) => {
    const resultDiv = document.getElementById('code-validation-result');
    if (!resultDiv) return;
    
    resultDiv.className = 'mb-4 p-3 rounded-lg text-sm';
    
    if (type === 'success') {
        resultDiv.className += ' bg-[var(--primary-accent)]/20 border border-[var(--primary-accent)]/50 text-[var(--primary-accent)]';
        resultDiv.innerHTML = `<i data-feather="check-circle" class="w-4 h-4 inline mr-2"></i>${message}`;
    } else if (type === 'error') {
        resultDiv.className += ' bg-red-500/20 border border-red-500/50 text-red-400';
        resultDiv.innerHTML = `<i data-feather="x-circle" class="w-4 h-4 inline mr-2"></i>${message}`;
    } else if (type === 'loading') {
        resultDiv.className += ' bg-[var(--cta-accent)]/20 border border-[var(--cta-accent)]/50 text-[var(--cta-accent)]';
        resultDiv.innerHTML = `<i data-feather="loader" class="w-4 h-4 inline mr-2 animate-spin"></i>${message}`;
    }
    
    resultDiv.classList.remove('hidden');
    if (typeof feather !== 'undefined') {
        feather.replace();
    }
};

// Validate Kit Code (Mock function - replace with Supabase)
const validateKitCode = async (code) => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // Mock validation logic
    const validCodes = {
        'ROBOT-NOLEP-ABC123': { kitId: 'robot-nolep', kitName: 'Robot Nolep Kit', used: false },
        'ROBOT-NOLEP-DEF456': { kitId: 'robot-nolep', kitName: 'Robot Nolep Kit', used: false },
        'WEATHER-STN-GHI789': { kitId: 'weather-station', kitName: 'Weather Station Kit', used: false },
        'IOT-SEC-JKL012': { kitId: 'iot-security', kitName: 'IoT Security Kit', used: false }
    };
    
    const codeData = validCodes[code];
    
    if (!codeData) {
        return { success: false, message: 'Kode tidak ditemukan!' };
    }
    
    if (codeData.used) {
        return { success: false, message: 'Kode sudah pernah digunakan!' };
    }
    
    // Mark as used (in real app, update database)
    validCodes[code].used = true;
    
    return {
        success: true,
        kitId: codeData.kitId,
        kitName: codeData.kitName,
        tutorials: kitTutorialMapping[codeData.kitId] || []
    };
};

// Update User Premium Access (Mock function - replace with Supabase)
const updateUserPremiumAccess = async (kitId, tutorials) => {
    // In real app, update user's premium access in database
    console.log('Updating user premium access:', { kitId, tutorials });
    
    // Store in localStorage for demo
    const userAccess = JSON.parse(localStorage.getItem('userPremiumAccess') || '{}');
    userAccess[kitId] = tutorials.map(t => t.id);
    localStorage.setItem('userPremiumAccess', JSON.stringify(userAccess));
};

// ==================== ADMIN CODE MANAGEMENT ========================

// Generate Kit Code Function
const generateKitCode = async (event) => {
    event.preventDefault();
    
    const kitSelect = document.getElementById('kit-select');
    const quantityInput = document.getElementById('code-quantity');
    const notesInput = document.getElementById('code-notes');
    
    const kitId = kitSelect.value;
    const quantity = parseInt(quantityInput.value);
    const notes = notesInput.value.trim();
    
    if (!kitId || quantity < 1) {
        alert('Pilih kit dan masukkan jumlah yang valid!');
        return;
    }
    
    try {
        // Use Supabase function if available, otherwise fallback to mock
        let result;
        if (typeof generateRedemptionCodes === 'function') {
            result = await generateRedemptionCodes(kitId, quantity, notes);
        } else {
            // Fallback to mock generation for development
            const codes = [];
            const kitPrefix = getKitPrefix(kitId);
            
            for (let i = 0; i < quantity; i++) {
                const randomCode = generateRandomCode();
                const fullCode = `${kitPrefix}-${randomCode}`;
                codes.push(fullCode);
            }
            
            result = { success: true, data: codes.map(code => ({ code })) };
            console.log('Generated codes (mock):', codes);
        }
        
        if (result.success) {
            const codeList = result.data.map(item => item.code).join('\n');
            alert(`Berhasil generate ${quantity} kode:\n\n${codeList}\n\nKode telah disimpan ke database.`);
            
            // Reset form
            document.getElementById('generate-code-form').reset();
            closeModal('generate-code-modal');
            
            // Refresh admin page
            if (document.getElementById('admin-page').style.display !== 'none') {
                location.reload();
            }
        } else {
            alert(`Gagal generate kode: ${result.error}`);
        }
        
    } catch (error) {
        console.error('Code generation error:', error);
        alert('Terjadi kesalahan saat generate kode. Silakan coba lagi.');
    }
};

// Get Kit Prefix
const getKitPrefix = (kitId) => {
    const prefixes = {
        'robot-nolep': 'ROBOT-NOLEP',
        'weather-station': 'WEATHER-STN',
        'disco-light': 'DISCO-LIGHT',
        'iot-security': 'IOT-SEC',
        'smart-garden': 'SMART-GARDEN',
        'led-matrix': 'LED-MATRIX'
    };
    return prefixes[kitId] || 'UNKNOWN-KIT';
};

// Generate Random Code
const generateRandomCode = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 6; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
};

// Copy Code Function
const copyCode = async (code) => {
    try {
        await navigator.clipboard.writeText(code);
        
        // Show temporary success message
        const button = event.target.closest('button');
        const originalIcon = button.innerHTML;
        button.innerHTML = '<i data-feather="check" class="w-4 h-4"></i>';
        button.classList.add('text-green-400');
        
        setTimeout(() => {
            button.innerHTML = originalIcon;
            button.classList.remove('text-green-400');
            if (typeof feather !== 'undefined') {
                feather.replace();
            }
        }, 1000);
        
        if (typeof feather !== 'undefined') {
            feather.replace();
        }
    } catch (error) {
        console.error('Copy failed:', error);
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = code;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        alert(`Kode disalin: ${code}`);
    }
};

// Delete Code Function
const deleteCode = (code) => {
    if (confirm(`Hapus kode ${code}?\n\nKode yang sudah dihapus tidak dapat dikembalikan.`)) {
        // In real app, delete from Supabase
        console.log('Deleting code:', code);
        alert('Kode berhasil dihapus!');
        
        // Remove from UI
        const row = event.target.closest('tr');
        if (row) {
            row.remove();
        }
    }
};

// ==================== DASHBOARD INITIALIZATION ========================

// Initialize Dashboard
const initializeDashboard = () => {
    // Load user's kits and progress
    loadUserKits();
    loadUserProgress();
    updateDashboardStats();
};

// Initialize Admin Dashboard
const initializeAdminDashboard = () => {
    // Load admin data
    loadAdminStats();
    loadRedemptionCodes();
};

// Initialize Projects Page
const initializeProjectsPage = () => {
    // Setup project filters
    setupProjectFilters();
};

// Initialize Tutorials Page
const initializeTutorialsPage = () => {
    // Setup tutorial filters
    setupTutorialFilters();
};

// Load User Kits (Mock function)
const loadUserKits = () => {
    // In real app, load from Supabase
    const userAccess = JSON.parse(localStorage.getItem('userPremiumAccess') || '{}');
    console.log('User kit access:', userAccess);
};

// Load User Progress (Mock function)
const loadUserProgress = () => {
    // In real app, load from Supabase
    console.log('Loading user progress...');
};

// Update Dashboard Stats (Mock function)
const updateDashboardStats = () => {
    // In real app, calculate from database
    console.log('Updating dashboard stats...');
};

// Load Admin Stats (Mock function)
const loadAdminStats = () => {
    // In real app, load from Supabase
    console.log('Loading admin stats...');
};

// Load Redemption Codes (Mock function)
const loadRedemptionCodes = () => {
    // In real app, load from Supabase
    console.log('Loading redemption codes...');
};

// Setup Project Filters
const setupProjectFilters = () => {
    // Initialize project filter functionality
    console.log('Setting up project filters...');
};

// Setup Tutorial Filters
const setupTutorialFilters = () => {
    // Initialize tutorial filter functionality
    console.log('Setting up tutorial filters...');
};
