# 🚀 Deployment Guide - Techno-Rebel IoT Kit Platform

Panduan lengkap untuk deploy platform ke production dengan Supabase + Vercel.

## 📋 Pre-requisites

- [ ] Akun <PERSON>itHub
- [ ] Akun Supabase (free tier)
- [ ] Akun Vercel (free tier)
- [ ] B<PERSON><PERSON> modern (Chrome/Firefox/Safari)

## 🗄️ Step 1: Setup Supabase Database

### 1.1 Create New Project
```bash
1. Buka https://supabase.com
2. Click "New Project"
3. Pilih organization atau buat baru
4. Project name: "techno-rebel-iot-platform"
5. Database password: Generate strong password (simpan!)
6. Region: "Southeast Asia (Singapore)" atau terdekat
7. Pricing plan: "Free"
8. Click "Create new project"
```

### 1.2 Setup Database Schema
```sql
1. Tunggu project selesai provisioning (~2 menit)
2. Buka "SQL Editor" di sidebar
3. Click "New query"
4. Copy-paste seluruh isi file `supabase-schema.sql`
5. Click "Run" untuk execute
6. Verify: Check "Table Editor" - harus ada 8 tables baru
```

### 1.3 Insert Sample Data
```sql
1. Di SQL Editor, buat query baru
2. Copy-paste seluruh isi file `sample-data.sql`
3. Click "Run" untuk execute
4. Verify: Check table "kits" - harus ada 6 kit sample
5. Verify: Check table "redemption_codes" - harus ada ~15 kode sample
```

### 1.4 Get API Credentials
```bash
1. Buka "Settings" > "API" di sidebar
2. Copy "Project URL" (format: https://xxx.supabase.co)
3. Copy "anon public" key (eyJ...)
4. Simpan kedua nilai ini untuk step selanjutnya
```

## ⚙️ Step 2: Configure Frontend

### 2.1 Update Supabase Config
```javascript
1. Buka file `supabase-config.js`
2. Replace placeholder values:

const SUPABASE_CONFIG = {
    url: 'https://YOUR_PROJECT_ID.supabase.co', // Paste Project URL
    anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...', // Paste anon key
    options: {
        auth: {
            autoRefreshToken: true,
            persistSession: true,
            detectSessionInUrl: true
        }
    }
};

3. Save file
```

### 2.2 Test Locally
```bash
1. Buka `index.html` di browser
2. Test basic navigation - semua page harus load
3. Test redemption code:
   - Click "Aktivasi Premium"
   - Input: ROBOT-NOLEP-ABC123
   - Harus muncul error "Silakan login terlebih dahulu"
4. Test admin dashboard:
   - Navigate ke admin page
   - Check kit code management section
```

## 🌐 Step 3: Deploy to Vercel

### 3.1 Prepare Git Repository
```bash
# Di terminal/command prompt, navigate ke folder project
cd "d:\web iot kit\landing page 1.1"

# Initialize git repository
git init

# Add all files
git add .

# Commit
git commit -m "Initial commit: IoT Kit Platform with Supabase integration"

# Create GitHub repository (via web atau CLI)
# Kemudian push:
git remote add origin https://github.com/YOUR_USERNAME/iot-kit-platform.git
git branch -M main
git push -u origin main
```

### 3.2 Deploy to Vercel
```bash
1. Buka https://vercel.com
2. Login dengan GitHub account
3. Click "New Project"
4. Import repository "iot-kit-platform"
5. Configure project:
   - Framework Preset: "Other"
   - Root Directory: "./"
   - Build Command: (leave empty)
   - Output Directory: (leave empty)
6. Click "Deploy"
7. Wait for deployment (~1-2 menit)
8. Get deployment URL (format: https://xxx.vercel.app)
```

### 3.3 Configure Custom Domain (Optional)
```bash
1. Di Vercel dashboard, buka project
2. Go to "Settings" > "Domains"
3. Add custom domain jika punya
4. Follow DNS configuration instructions
```

## 🔐 Step 4: Setup Authentication

### 4.1 Configure Auth Settings
```bash
1. Di Supabase dashboard, buka "Authentication" > "Settings"
2. Site URL: https://YOUR_VERCEL_URL.vercel.app
3. Redirect URLs: 
   - https://YOUR_VERCEL_URL.vercel.app
   - http://localhost:3000 (untuk development)
4. Email templates: Customize jika perlu
5. Save configuration
```

### 4.2 Create Admin User
```bash
1. Di Supabase, buka "Authentication" > "Users"
2. Click "Add user"
3. Email: <EMAIL> (atau email Anda)
4. Password: Generate strong password
5. Email confirm: true
6. Click "Create user"
7. Copy User ID (UUID)
```

### 4.3 Set Admin Role
```sql
1. Di SQL Editor, jalankan query:

UPDATE public.user_profiles 
SET role = 'admin' 
WHERE id = 'USER_ID_DARI_STEP_SEBELUMNYA';

2. Verify: Check table user_profiles
```

## 🧪 Step 5: Testing & Verification

### 5.1 Test User Registration
```bash
1. Buka deployed website
2. Click "Daftar Sekarang"
3. Register dengan email baru
4. Check email untuk verification link
5. Verify account
6. Login berhasil
```

### 5.2 Test Code Redemption
```bash
1. Login sebagai user biasa
2. Click "Aktivasi Premium"
3. Input kode: ROBOT-NOLEP-ABC123
4. Harus berhasil dan redirect ke dashboard
5. Check dashboard - harus muncul kit baru
6. Verify di Supabase: table user_kits harus ada entry baru
```

### 5.3 Test Admin Functions
```bash
1. Login sebagai admin
2. Navigate ke admin dashboard
3. Test generate kode:
   - Click "Generate Kode"
   - Pilih kit: Robot Nolep Kit
   - Quantity: 5
   - Submit
4. Verify: Table redemption_codes harus ada 5 kode baru
```

## 📊 Step 6: Production Configuration

### 6.1 Environment Variables (Optional)
```bash
# Di Vercel dashboard > Settings > Environment Variables
# Add jika diperlukan:
SUPABASE_URL=https://xxx.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
NODE_ENV=production
```

### 6.2 Performance Optimization
```bash
1. Enable Vercel Analytics (optional)
2. Setup custom error pages
3. Configure caching headers
4. Optimize images jika ada
```

### 6.3 Security Checklist
```bash
- [ ] RLS policies enabled di semua tables
- [ ] API keys tidak exposed di client-side
- [ ] HTTPS enforced (automatic di Vercel)
- [ ] Email verification required
- [ ] Strong password requirements
- [ ] Admin role properly restricted
```

## 🔧 Step 7: Maintenance & Monitoring

### 7.1 Setup Monitoring
```bash
1. Supabase Dashboard > Settings > API
   - Monitor API usage
   - Check database size
   - Review slow queries

2. Vercel Dashboard > Analytics
   - Monitor page views
   - Check performance metrics
   - Review error logs
```

### 7.2 Backup Strategy
```bash
1. Supabase automatic backups (7 days retention di free tier)
2. Manual backup via SQL export jika perlu
3. Code repository di GitHub sebagai backup
```

### 7.3 Update Procedures
```bash
1. Test changes locally
2. Push to GitHub
3. Vercel auto-deploy dari main branch
4. Test di production
5. Monitor for issues
```

## 🚨 Troubleshooting

### Common Issues & Solutions

#### 1. Supabase Connection Error
```javascript
// Check browser console
// Error: "Failed to fetch"
// Solution: Verify API URL dan key di supabase-config.js
```

#### 2. Authentication Not Working
```bash
# Check Supabase Auth settings
# Verify Site URL dan Redirect URLs
# Check email verification status
```

#### 3. RLS Policy Blocking Access
```sql
-- Check policies di Supabase
-- Verify user role di user_profiles table
-- Test policies dengan different users
```

#### 4. Vercel Deployment Failed
```bash
# Check build logs di Vercel dashboard
# Verify file structure
# Check for syntax errors
```

## 📞 Support & Resources

### Documentation
- [Supabase Docs](https://supabase.com/docs)
- [Vercel Docs](https://vercel.com/docs)
- [Project README](./README.md)

### Support Channels
- GitHub Issues untuk bug reports
- Email: <EMAIL>
- Supabase Community untuk database issues

---

## ✅ Deployment Checklist

Sebelum go-live, pastikan semua item ini sudah completed:

### Database
- [ ] Schema deployed successfully
- [ ] Sample data inserted
- [ ] RLS policies active
- [ ] Admin user created

### Frontend
- [ ] Supabase config updated
- [ ] Local testing passed
- [ ] Deployed to Vercel
- [ ] Custom domain configured (optional)

### Authentication
- [ ] Auth settings configured
- [ ] Email verification working
- [ ] Admin role assigned
- [ ] User registration tested

### Features
- [ ] Code redemption working
- [ ] Admin dashboard functional
- [ ] Kit-tutorial relationships correct
- [ ] Progress tracking active

### Production
- [ ] Performance optimized
- [ ] Security checklist completed
- [ ] Monitoring setup
- [ ] Backup strategy in place

**🎉 Congratulations! Your IoT Kit Platform is now live!**
