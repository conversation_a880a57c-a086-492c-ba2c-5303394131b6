# 🚀 Techno-Rebel IoT Kit Platform

Platform landing page untuk kit IoT dengan sistem redemption code yang menghubungkan pembelian kit di Tokopedia dengan akses tutorial premium di website.

## ✨ Fitur Utama

### 🎯 Sistem Redemption Code
- **Multi-channel Purchase Flow**: Kit dibeli di Tokopedia, tutorial premium via WhatsApp
- **Automatic Access Granting**: Kode redemption memberikan akses otomatis ke tutorial premium
- **Code Format**: `KIT-NAME-RANDOMCODE` (contoh: `ROBOT-NOLEP-ABC123`)
- **Admin Code Management**: Generate, track, dan manage kode redemption

### 👤 User Experience
- **Enhanced Dashboard**: Stats overview, kit management, progress tracking
- **Kit-Tutorial Relationship**: Visual connection antara kit dan tutorial premium
- **Responsive Design**: Mobile-first dengan glass morphism design
- **Multi-language Support**: Indonesian/English toggle

### 🔐 Admin Features
- **Code Generation**: Batch generate kode untuk setiap kit
- **Code Tracking**: Monitor status penggunaan kode
- **User Management**: Kelola user dan akses mereka
- **Analytics Dashboard**: Overview statistik platform

## 🛠️ Tech Stack

- **Frontend**: HTML5, CSS3 (Tailwind), Modular JavaScript
- **Backend**: Supabase (Database + Auth + Real-time)
- **Deployment**: Vercel (Frontend) + Supabase (Backend)
- **Icons**: Feather Icons
- **Design**: Quantum Core theme dengan glass morphism
- **Architecture**: Single Page Application (SPA) dengan modular JavaScript files

## 📁 File Structure (Refactored)

Aplikasi telah direfactor menjadi modular JavaScript files untuk maintainability yang lebih baik:

```
├── index.html                 # Main HTML file (UI only)
├── deployment-config.js       # Environment & deployment configuration
├── supabase-config.js         # Supabase client & database functions
├── app.js                     # Core navigation & UI functions
├── authentication.js          # User authentication system
├── kit-tutorial-system.js     # Kit & tutorial management
├── admin-functions.js         # Admin panel functions
├── progress-tracking.js       # User progress & achievements
├── supabase-schema.sql        # Database schema
├── sample-data.sql           # Sample data for testing
├── vercel.json               # Vercel deployment config
├── .env.example              # Environment variables template
├── README.md                 # Documentation
└── deploy.md                 # Deployment guide
```

### JavaScript Modules:

- **deployment-config.js**: Environment detection, feature flags, logging
- **supabase-config.js**: Database operations, authentication API
- **app.js**: Page navigation, modals, language switching
- **authentication.js**: Login, register, logout, session management
- **kit-tutorial-system.js**: Kit purchases, code redemption, tutorial access
- **admin-functions.js**: Admin panel, user management, content management
- **progress-tracking.js**: Tutorial progress, achievements, analytics

## 📦 Setup & Deployment

### 1. Supabase Setup

1. **Buat Project Baru di Supabase**
   ```bash
   # Buka https://supabase.com
   # Create new project
   # Pilih region: us-east-1 (atau terdekat)
   # Pilih plan: Free tier
   ```

2. **Setup Database Schema**
   ```sql
   # Copy paste isi file supabase-schema.sql ke SQL Editor di Supabase
   # Jalankan query untuk membuat tables dan policies
   ```

3. **Insert Sample Data**
   ```sql
   # Copy paste isi file sample-data.sql ke SQL Editor
   # Jalankan untuk insert data kit, tutorial, dan kode sample
   ```

4. **Get API Keys**
   ```bash
   # Di Supabase Dashboard > Settings > API
   # Copy: Project URL dan anon public key
   ```

### 2. Frontend Configuration

1. **Update Supabase Config**
   ```javascript
   // Edit file supabase-config.js
   const SUPABASE_CONFIG = {
       url: 'YOUR_SUPABASE_PROJECT_URL',
       anonKey: 'YOUR_SUPABASE_ANON_KEY'
   };
   ```

2. **Test Locally**
   ```bash
   # Buka index.html di browser
   # Test fitur redemption code dengan sample data
   # Test admin dashboard untuk generate kode
   ```

### 3. Vercel Deployment

1. **Prepare Repository**
   ```bash
   git init
   git add .
   git commit -m "Initial commit: IoT Kit Platform with redemption system"
   git remote add origin YOUR_GITHUB_REPO_URL
   git push -u origin main
   ```

2. **Deploy ke Vercel**
   ```bash
   # Buka https://vercel.com
   # Import project dari GitHub
   # Deploy dengan default settings
   ```

3. **Environment Variables** (Optional)
   ```bash
   # Di Vercel Dashboard > Settings > Environment Variables
   # Tambahkan jika diperlukan untuk production config
   ```

## 🔑 Sample Redemption Codes

Untuk testing, gunakan kode-kode berikut:

### Robot Nolep Kit
- `ROBOT-NOLEP-ABC123`
- `ROBOT-NOLEP-DEF456`
- `ROBOT-NOLEP-GHI789`

### Weather Station Kit
- `WEATHER-STN-ABC123`
- `WEATHER-STN-DEF456`

### IoT Security Kit
- `IOT-SEC-ABC123`
- `IOT-SEC-DEF456`

## 📱 User Flow

### Customer Journey
1. **Discover**: Browse kit di landing page
2. **Purchase**: Beli kit di Tokopedia
3. **Redeem**: Dapat kode redemption dari penjual
4. **Access**: Input kode di website untuk akses tutorial premium
5. **Learn**: Akses tutorial premium sesuai kit yang dibeli

### Admin Workflow
1. **Generate**: Buat batch kode untuk kit tertentu
2. **Distribute**: Berikan kode ke customer Tokopedia
3. **Monitor**: Track penggunaan kode di dashboard
4. **Manage**: Kelola user access dan progress

## 🎨 Design System

### Color Palette
- **Background**: `#120D21` (Dark purple)
- **Primary Accent**: `#00F5D4` (Turquoise neon)
- **Secondary Accent**: `#9B5DE5` (Medium purple)
- **CTA Accent**: `#FEE440` (Golden yellow)

### Typography
- **Headers**: 'Orbitron' (Tech/futuristic)
- **Body**: 'Inter' (Clean/readable)
- **Code**: 'JetBrains Mono' (Monospace)

### Components
- **Glass Cards**: Backdrop blur dengan border accent
- **Buttons**: Gradient dengan hover effects
- **Modals**: Centered dengan glass morphism
- **Forms**: Floating labels dengan validation

## 🔒 Security Features

### Row Level Security (RLS)
- Users hanya bisa akses data mereka sendiri
- Admin memiliki akses penuh untuk management
- Public read untuk kit dan tutorial information

### Authentication
- Supabase Auth dengan email verification
- JWT token untuk session management
- Secure password requirements

### Data Protection
- Redemption codes dengan expiry date
- One-time use validation
- Audit trail untuk code usage

## 📊 Analytics & Monitoring

### User Metrics
- Kit ownership tracking
- Tutorial completion rates
- User engagement analytics
- Progress tracking per tutorial

### Business Metrics
- Code redemption rates
- Popular kit analysis
- Revenue attribution
- Customer journey analytics

## 🚀 Future Enhancements

### Phase 2 Features
- [ ] Email notifications untuk code redemption
- [ ] Advanced analytics dashboard
- [ ] Bulk user management
- [ ] API untuk integrasi Tokopedia
- [ ] Mobile app companion

### Phase 3 Features
- [ ] Live streaming tutorial integration
- [ ] Community forum
- [ ] Achievement system
- [ ] Referral program
- [ ] Advanced project gallery

## 🐛 Troubleshooting

### Common Issues

1. **Supabase Connection Error**
   ```javascript
   // Check console untuk error messages
   // Verify API keys di supabase-config.js
   // Check network connectivity
   ```

2. **Code Redemption Failed**
   ```javascript
   // Verify code format: KIT-NAME-CODE
   // Check if code exists in database
   // Ensure user is authenticated
   ```

3. **Admin Dashboard Not Loading**
   ```javascript
   // Check user role in database
   // Verify RLS policies
   // Check browser console for errors
   ```

## 📞 Support

Untuk bantuan teknis atau pertanyaan:
- **Email**: <EMAIL>
- **GitHub Issues**: Create issue di repository
- **Documentation**: Check file ini dan komentar di kode

## 📄 License

MIT License - Feel free to use and modify for your projects.

---

**Built with ❤️ by Techno-Rebel Team**

*Empowering the next generation of IoT developers through hands-on learning experiences.*
