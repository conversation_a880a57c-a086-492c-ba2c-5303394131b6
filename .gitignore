# ==================== ENVIRONMENT VARIABLES ========================
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# ==================== DEPENDENCIES ========================
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# ==================== BUILD OUTPUTS ========================
dist/
build/
.next/
.nuxt/
.vuepress/dist/

# ==================== CACHE DIRECTORIES ========================
.cache/
.parcel-cache/
.vite/
.turbo/

# ==================== LOGS ========================
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# ==================== RUNTIME DATA ========================
pids/
*.pid
*.seed
*.pid.lock

# ==================== COVERAGE DIRECTORY ========================
coverage/
*.lcov
.nyc_output/

# ==================== DEPENDENCY DIRECTORIES ========================
jspm_packages/

# ==================== TYPESCRIPT ========================
*.tsbuildinfo

# ==================== OPTIONAL NPM CACHE ========================
.npm/

# ==================== OPTIONAL ESLINT CACHE ========================
.eslintcache

# ==================== MICROBUNDLE CACHE ========================
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# ==================== OPTIONAL REPL HISTORY ========================
.node_repl_history

# ==================== OUTPUT OF 'NPM PACK' ========================
*.tgz

# ==================== YARN INTEGRITY FILE ========================
.yarn-integrity

# ==================== PARCEL-BUNDLER CACHE ========================
.parcel-cache/

# ==================== NEXT.JS BUILD OUTPUT ========================
.next/
out/

# ==================== NUXT.JS BUILD / GENERATE OUTPUT ========================
.nuxt/
dist/

# ==================== GATSBY FILES ========================
.cache/
public/

# ==================== VUEPRESS BUILD OUTPUT ========================
.vuepress/dist/

# ==================== SERVERLESS DIRECTORIES ========================
.serverless/

# ==================== FUSEBOX CACHE ========================
.fusebox/

# ==================== DYNAMODB LOCAL FILES ========================
.dynamodb/

# ==================== TERNJS PORT FILE ========================
.tern-port

# ==================== VERCEL ========================
.vercel

# ==================== SUPABASE ========================
supabase/.branches
supabase/.temp

# ==================== EDITOR DIRECTORIES AND FILES ========================
.vscode/
.idea/
*.swp
*.swo
*~

# ==================== OS GENERATED FILES ========================
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# ==================== TEMPORARY FILES ========================
*.tmp
*.temp
temp/
tmp/

# ==================== BACKUP FILES ========================
*.bak
*.backup
*~

# ==================== LOCAL DEVELOPMENT ========================
.local/
local/

# ==================== TESTING ========================
.jest/
coverage/

# ==================== DOCUMENTATION ========================
docs/.vitepress/cache/
docs/.vitepress/dist/
