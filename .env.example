# ==================== SUPABASE CONFIGURATION ========================
# Get these values from your Supabase project dashboard
# Project Settings > API > Project URL and Project API keys

# Supabase Project URL
VITE_SUPABASE_URL=https://your-project-id.supabase.co

# Supabase Anonymous Key (Public)
VITE_SUPABASE_ANON_KEY=your-anon-key-here

# ==================== OPTIONAL CONFIGURATIONS ========================

# Environment (development, staging, production)
VITE_ENVIRONMENT=production

# Enable/disable features
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_DEBUG=false

# WhatsApp Business Number (for premium tutorial purchases)
VITE_WHATSAPP_NUMBER=6281234567890

# Tokopedia Store URL
VITE_TOKOPEDIA_STORE=https://tokopedia.com/techno-rebel

# ==================== ANALYTICS (OPTIONAL) ========================

# Google Analytics 4 Measurement ID
VITE_GA_MEASUREMENT_ID=G-XXXXXXXXXX

# ==================== DEPLOYMENT NOTES ========================
# 
# 1. Copy this file to .env.local for local development
# 2. Set environment variables in Vercel dashboard for production
# 3. Never commit actual .env files to version control
# 4. Only VITE_ prefixed variables are available in the browser
#
