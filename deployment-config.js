// ==================== DEPLOYMENT CONFIGURATION ========================
// Deployment and environment configuration for the IoT Kit Platform
// Created: 2025-08-01

// ==================== ENVIRONMENT DETECTION ========================

// Detect current environment
const detectEnvironment = () => {
    const hostname = window.location.hostname;
    
    if (hostname === 'localhost' || hostname === '127.0.0.1' || hostname.includes('192.168')) {
        return 'development';
    } else if (hostname.includes('vercel.app') || hostname.includes('netlify.app')) {
        return 'production';
    } else if (hostname.includes('staging') || hostname.includes('preview')) {
        return 'staging';
    } else {
        return 'production'; // Default to production for custom domains
    }
};

// Get current environment
const ENVIRONMENT = detectEnvironment();

// ==================== CONFIGURATION OBJECTS ========================

// Development Configuration
const developmentConfig = {
    environment: 'development',
    debug: true,
    supabase: {
        enabled: false, // Use mock functions in development
        url: '', // Will be set when Supabase is configured
        anonKey: '' // Will be set when Supabase is configured
    },
    features: {
        authentication: true,
        progressTracking: true,
        adminPanel: true,
        analytics: false
    },
    api: {
        baseUrl: 'http://localhost:3000',
        timeout: 5000
    },
    storage: {
        type: 'localStorage',
        prefix: 'iot_kit_dev_'
    }
};

// Production Configuration
const productionConfig = {
    environment: 'production',
    debug: false,
    supabase: {
        enabled: true,
        url: process.env.VITE_SUPABASE_URL || '',
        anonKey: process.env.VITE_SUPABASE_ANON_KEY || ''
    },
    features: {
        authentication: true,
        progressTracking: true,
        adminPanel: true,
        analytics: true
    },
    api: {
        baseUrl: 'https://api.iotkit.com',
        timeout: 10000
    },
    storage: {
        type: 'supabase',
        prefix: 'iot_kit_'
    }
};

// Staging Configuration
const stagingConfig = {
    environment: 'staging',
    debug: true,
    supabase: {
        enabled: true,
        url: process.env.VITE_SUPABASE_URL || '',
        anonKey: process.env.VITE_SUPABASE_ANON_KEY || ''
    },
    features: {
        authentication: true,
        progressTracking: true,
        adminPanel: true,
        analytics: false
    },
    api: {
        baseUrl: 'https://staging-api.iotkit.com',
        timeout: 8000
    },
    storage: {
        type: 'supabase',
        prefix: 'iot_kit_staging_'
    }
};

// ==================== CONFIGURATION SELECTOR ========================

// Get configuration based on environment
const getConfig = () => {
    switch (ENVIRONMENT) {
        case 'development':
            return developmentConfig;
        case 'staging':
            return stagingConfig;
        case 'production':
            return productionConfig;
        default:
            return productionConfig;
    }
};

// Current configuration
const CONFIG = getConfig();

// ==================== FEATURE FLAGS ========================

// Check if feature is enabled
const isFeatureEnabled = (featureName) => {
    return CONFIG.features[featureName] || false;
};

// Feature flag functions
const features = {
    authentication: () => isFeatureEnabled('authentication'),
    progressTracking: () => isFeatureEnabled('progressTracking'),
    adminPanel: () => isFeatureEnabled('adminPanel'),
    analytics: () => isFeatureEnabled('analytics')
};

// ==================== LOGGING SYSTEM ========================

// Logger with environment-aware levels
const logger = {
    debug: (...args) => {
        if (CONFIG.debug) {
            console.log('[DEBUG]', ...args);
        }
    },
    info: (...args) => {
        console.info('[INFO]', ...args);
    },
    warn: (...args) => {
        console.warn('[WARN]', ...args);
    },
    error: (...args) => {
        console.error('[ERROR]', ...args);
    }
};

// ==================== ANALYTICS INTEGRATION ========================

// Initialize analytics if enabled
const initializeAnalytics = () => {
    if (!features.analytics()) {
        logger.debug('Analytics disabled in current environment');
        return;
    }

    // Google Analytics 4 (example)
    if (typeof gtag !== 'undefined') {
        gtag('config', 'GA_MEASUREMENT_ID', {
            page_title: 'IoT Kit Platform',
            page_location: window.location.href
        });
        logger.info('Google Analytics initialized');
    }

    // Custom analytics events
    window.trackEvent = (eventName, parameters = {}) => {
        if (features.analytics() && typeof gtag !== 'undefined') {
            gtag('event', eventName, {
                ...parameters,
                environment: CONFIG.environment
            });
        }
        logger.debug('Event tracked:', eventName, parameters);
    };
};

// ==================== ERROR HANDLING ========================

// Global error handler
const setupErrorHandling = () => {
    window.addEventListener('error', (event) => {
        logger.error('Global error:', event.error);
        
        if (features.analytics()) {
            window.trackEvent('error', {
                message: event.error.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno
            });
        }
    });

    window.addEventListener('unhandledrejection', (event) => {
        logger.error('Unhandled promise rejection:', event.reason);
        
        if (features.analytics()) {
            window.trackEvent('promise_rejection', {
                reason: event.reason.toString()
            });
        }
    });
};

// ==================== PERFORMANCE MONITORING ========================

// Performance monitoring
const setupPerformanceMonitoring = () => {
    if (!features.analytics()) {
        return;
    }

    // Measure page load time
    window.addEventListener('load', () => {
        setTimeout(() => {
            const perfData = performance.getEntriesByType('navigation')[0];
            if (perfData) {
                window.trackEvent('page_load_time', {
                    load_time: Math.round(perfData.loadEventEnd - perfData.fetchStart),
                    dom_content_loaded: Math.round(perfData.domContentLoadedEventEnd - perfData.fetchStart)
                });
            }
        }, 0);
    });
};

// ==================== SUPABASE CONFIGURATION ========================

// Initialize Supabase based on environment
const initializeSupabase = () => {
    if (!CONFIG.supabase.enabled) {
        logger.info('Supabase disabled, using mock functions');
        return null;
    }

    if (!CONFIG.supabase.url || !CONFIG.supabase.anonKey) {
        logger.warn('Supabase credentials not found, falling back to mock functions');
        return null;
    }

    try {
        if (typeof supabase !== 'undefined' && typeof supabase.createClient === 'function') {
            const client = supabase.createClient(CONFIG.supabase.url, CONFIG.supabase.anonKey);
            logger.info('Supabase client initialized successfully');
            return client;
        } else {
            logger.warn('Supabase library not loaded, falling back to mock functions');
            return null;
        }
    } catch (error) {
        logger.error('Failed to initialize Supabase:', error);
        return null;
    }
};

// ==================== DEPLOYMENT CHECKS ========================

// Check deployment health
const checkDeploymentHealth = async () => {
    const checks = {
        environment: CONFIG.environment,
        supabase: CONFIG.supabase.enabled,
        features: CONFIG.features,
        timestamp: new Date().toISOString()
    };

    logger.info('Deployment health check:', checks);

    // Test Supabase connection if enabled
    if (CONFIG.supabase.enabled) {
        try {
            const client = initializeSupabase();
            if (client) {
                // Simple health check query
                const { data, error } = await client.from('user_profiles').select('count').limit(1);
                checks.supabaseConnection = !error;
                if (error) {
                    logger.warn('Supabase connection test failed:', error);
                }
            }
        } catch (error) {
            logger.error('Supabase health check failed:', error);
            checks.supabaseConnection = false;
        }
    }

    return checks;
};

// ==================== INITIALIZATION ========================

// Initialize deployment configuration
const initializeDeployment = async () => {
    logger.info(`Initializing IoT Kit Platform in ${CONFIG.environment} environment`);
    
    // Setup error handling
    setupErrorHandling();
    
    // Setup performance monitoring
    setupPerformanceMonitoring();
    
    // Initialize analytics
    initializeAnalytics();
    
    // Initialize Supabase
    const supabaseClient = initializeSupabase();
    
    // Store global references
    window.CONFIG = CONFIG;
    window.logger = logger;
    window.features = features;
    window.supabaseClient = supabaseClient;
    
    // Run health check
    const healthCheck = await checkDeploymentHealth();
    logger.info('Deployment initialized successfully:', healthCheck);
    
    return {
        config: CONFIG,
        supabaseClient,
        healthCheck
    };
};

// ==================== EXPORT CONFIGURATION ========================

// Make configuration available globally
window.deploymentConfig = {
    CONFIG,
    ENVIRONMENT,
    logger,
    features,
    initializeDeployment,
    checkDeploymentHealth
};

// Auto-initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    initializeDeployment();
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        CONFIG,
        ENVIRONMENT,
        logger,
        features,
        initializeDeployment,
        checkDeploymentHealth
    };
}
