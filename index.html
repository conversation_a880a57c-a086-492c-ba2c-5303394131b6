<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Techno Rebel - Portal <PERSON></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;700&family=Roboto+Mono:wght@400;700&family=Share+Tech+Mono&family=Space+Mono:wght@400;700&display=swap" rel="stylesheet">
    
    <!-- Feather Icons -->
    <script src="https://unpkg.com/feather-icons"></script>
    
    <!-- Prism.js for Code Highlighting -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-okaidia.min.css" rel="stylesheet" />

    <style>
        /* Palet warna dan gaya dasar (Quantum Core) */
        :root {
            --bg-dark: #120D21;
            --primary-accent: #00F5D4; /* Turquoise Neon */
            --secondary-accent: #9B5DE5; /* Medium Purple */
            --cta-accent: #FEE440; /* Golden Yellow */
            --text-primary: #EAEAEA; /* Ivory White */
            --text-secondary: #8892B0; /* Slate Grey */
        }

        html { scroll-behavior: smooth; }
        body {
            background-color: var(--bg-dark);
            color: var(--text-primary);
            font-family: 'Inter', sans-serif;
        }

        /* Tipografi */
        .font-header { font-family: 'Space Mono', monospace; }
        .font-body { font-family: 'Inter', sans-serif; }
        .font-label { font-family: 'Roboto Mono', monospace; }
        .font-tech-mono { font-family: 'Share Tech Mono', monospace; }

        /* Efek Kaca (Diperbarui) */
        .glass-card { background: rgba(0, 245, 212, 0.05); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); border: 1px solid rgba(0, 245, 212, 0.1); }

        /* Progress Bar (Diperbarui) */
        .progress-bar-container { background-color: rgba(0,0,0,0.5); border: 1px solid var(--text-secondary); }
        .progress-bar { background-color: var(--primary-accent); box-shadow: 0 0 10px var(--primary-accent); }

        /* Menyembunyikan semua halaman/modal secara default */
        .page, .modal-container, .tutorial-tab-content { display: none; }
        .page.active, .modal-container.active, .tutorial-tab-content.active { display: block; }
        .modal-container.active { display: flex; }
        
        /* Custom scrollbar */
        .custom-scrollbar::-webkit-scrollbar { width: 8px; }
        .custom-scrollbar::-webkit-scrollbar-track { background: transparent; }
        .custom-scrollbar::-webkit-scrollbar-thumb { background-color: var(--text-secondary); border-radius: 20px; border: 3px solid var(--bg-dark); }
        
        /* Efek Glitch (Diperbarui) */
        .glitch-text { position: relative; display: inline-block; }
        .glitch-text::before, .glitch-text::after { content: attr(data-text); position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: var(--bg-dark); overflow: hidden; }
        .glitch-text::before { left: 2px; text-shadow: -2px 0 var(--primary-accent); animation: glitch-anim-1 2s infinite linear alternate-reverse; }
        .glitch-text::after { left: -2px; text-shadow: -2px 0 var(--secondary-accent), 2px 2px var(--cta-accent); animation: glitch-anim-2 2s infinite linear alternate-reverse; }
        @keyframes glitch-anim-1 { 0% { clip-path: inset(10% 0 80% 0); } 20% { clip-path: inset(50% 0 10% 0); } 40% { clip-path: inset(25% 0 60% 0); } 60% { clip-path: inset(80% 0 5% 0); } 80% { clip-path: inset(15% 0 70% 0); } 100% { clip-path: inset(60% 0 30% 0); } }
        @keyframes glitch-anim-2 { 0% { clip-path: inset(85% 0 5% 0); } 20% { clip-path: inset(20% 0 75% 0); } 40% { clip-path: inset(65% 0 15% 0); } 60% { clip-path: inset(40% 0 40% 0); } 80% { clip-path: inset(90% 0 2% 0); } 100% { clip-path: inset(5% 0 88% 0); } }
        
        /* Loader */
        .loader-container { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(18, 13, 33, 0.8); z-index: 9999; display: none; align-items: center; justify-content: center; backdrop-filter: blur(5px); }
        .loader { color: var(--primary-accent); font-family: 'Roboto Mono', monospace; font-size: 20px; }
        .loader::before { content: '/>_'; display: inline-block; animation: loader-anim 1.5s infinite; }
        @keyframes loader-anim {
          0% { content: '/>_'; } 25% { content: '/>'; } 50% { content: '>'; } 75% { content: ''; } 100% { content: '/>_'; }
        }

        /* Professional Button (Diperbarui) */
        .btn-pro {
          font-family: 'Roboto Mono', monospace;
          background: transparent;
          padding: 0.8em 1.8em;
          border: 2px solid var(--primary-accent);
          position: relative;
          overflow: hidden;
          color: var(--primary-accent);
          transition: all 1s;
          border-radius: 0.75rem;
          font-size: 1rem;
          font-weight: bold;
          cursor: pointer;
        }
        .btn-pro:before {
          content: "";
          position: absolute;
          left: 0;
          top: 0;
          width: 0;
          height: 100%;
          background: var(--primary-accent);
          z-index: -1;
          transition: all 0.5s;
        }
        .btn-pro:hover {
          color: var(--bg-dark);
        }
        .btn-pro:hover:before {
          width: 100%;
        }

        /* Professional Input (Diperbarui) */
        .input-group {
          position: relative;
        }
        .input-pro {
          border: solid 1.5px var(--text-secondary);
          border-radius: 1rem;
          background: none;
          padding: 1rem;
          width: 100%;
          font-size: 1rem;
          color: var(--text-primary);
          transition: border 150ms cubic-bezier(0.4,0,0.2,1);
          -webkit-appearance: none;
          -moz-appearance: none;
          appearance: none;
        }
        select.input-pro {
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%238892B0' viewBox='0 0 16 16'%3E%3Cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 1rem center;
            background-size: 1em;
        }
        .input-pro option {
            background-color: var(--bg-dark);
            color: var(--text-primary);
        }
        .input-label {
          position: absolute;
          left: 15px;
          color: var(--text-secondary);
          pointer-events: none;
          transform: translateY(1rem);
          transition: 150ms cubic-bezier(0.4,0,0.2,1);
        }
        .input-pro:focus, .input-pro:not(:placeholder-shown) {
          outline: none;
          border: 1.5px solid var(--primary-accent);
        }
        .input-pro:focus ~ .input-label,
        .input-pro:not(:placeholder-shown) ~ .input-label {
          transform: translateY(-50%) scale(0.8);
          background-color: var(--bg-dark);
          padding: 0 .2em;
          color: var(--primary-accent);
        }
        
        /* 3D Tilt Card Effect */
        .card-tilt {
            transition: transform 0.5s;
            transform-style: preserve-3d;
        }

        /* Mobile Menu Toggle */
        .mobile-menu {
            display: none;
        }
        .mobile-menu.active {
            display: block;
        }

        /* Responsive improvements */
        @media (max-width: 768px) {
            .hidden-mobile { display: none !important; }
            .mobile-menu-button { display: block; }
            .mobile-menu {
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background: rgba(18, 13, 33, 0.95);
                backdrop-filter: blur(10px);
                border-top: 1px solid rgba(0, 245, 212, 0.1);
                padding: 1rem;
            }
            .mobile-menu a {
                display: block;
                padding: 0.75rem 0;
                border-bottom: 1px solid rgba(0, 245, 212, 0.1);
            }
            .mobile-menu a:last-child {
                border-bottom: none;
            }
        }
    </style>
</head>
<body class="bg-dark font-body text-text-primary antialiased">

    <!-- Loader -->
    <div id="loader" class="loader-container">
        <span class="loader"></span>
    </div>

    <!-- Container untuk semua halaman -->
    <div id="app-container">

        <!-- ======================= HALAMAN LANDING PAGE ======================= -->
        <div id="landing-page" class="page">
            <header class="sticky top-0 z-50 glass-card">
                <nav class="container mx-auto px-6 py-4 relative">
                    <div class="flex justify-between items-center">
                        <a href="#" onclick="showPage('landing-page')" class="font-header text-2xl glitch-text" data-text="TECHNO-REBEL">TECHNO-REBEL</a>
                        <div class="hidden md:flex items-center space-x-8 font-label">
                            <a href="#" onclick="showPage('kits-list-page')" class="text-[var(--text-secondary)] hover:text-[var(--primary-accent)] transition-colors" data-lang-id="Etalase Proyek" data-lang-en="Project Catalog"></a>
                            <a href="#" onclick="showPage('tutorials-list-page')" class="text-[var(--text-secondary)] hover:text-[var(--primary-accent)] transition-colors" data-lang-id="Tutorial Hype" data-lang-en="Hype Tutorials"></a>
                        </div>
                        <div class="flex items-center space-x-4">
                            <a href="#" onclick="showPage('login-page')" class="hidden md:block px-4 py-2 rounded-lg text-sm font-label border-2 border-[var(--primary-accent)] text-[var(--primary-accent)] hover:bg-[var(--primary-accent)] hover:text-black transition-colors" data-lang-id="LOGIN" data-lang-en="LOGIN"></a>
                            <button class="md:hidden mobile-menu-button text-[var(--primary-accent)]" onclick="toggleMobileMenu()">
                                <i data-feather="menu"></i>
                            </button>
                        </div>
                    </div>
                    <div id="mobile-menu" class="mobile-menu">
                        <a href="#" onclick="showPage('kits-list-page'); toggleMobileMenu()" class="text-[var(--text-secondary)] hover:text-[var(--primary-accent)] transition-colors" data-lang-id="Etalase Proyek" data-lang-en="Project Catalog"></a>
                        <a href="#" onclick="showPage('tutorials-list-page'); toggleMobileMenu()" class="text-[var(--text-secondary)] hover:text-[var(--primary-accent)] transition-colors" data-lang-id="Tutorial Hype" data-lang-en="Hype Tutorials"></a>
                        <a href="#" onclick="showPage('login-page'); toggleMobileMenu()" class="text-[var(--primary-accent)] hover:text-white transition-colors" data-lang-id="LOGIN" data-lang-en="LOGIN"></a>
                    </div>
                </nav>
            </header>

            <main class="container mx-auto px-6 py-16 md:py-24">
                <section id="home" class="text-center">
                    <div class="relative inline-block mb-8">
                        <img src="https://placehold.co/400x300/120D21/00F5D4?text=ESP32+KIT" alt="[Gambar dari Kit ESP32]" class="rounded-lg shadow-2xl border-2 border-[var(--text-secondary)]" style="box-shadow: 0 0 25px rgba(0, 245, 212, 0.3), 0 0 40px rgba(0, 245, 212, 0.2);">
                    </div>
                    <h1 class="font-header text-4xl md:text-6xl font-bold mb-4" data-lang-id="POV: Lo Nggak Cuma Nonton, <span class='text-[var(--primary-accent)]'>Lo yang Bikin.</span>" data-lang-en="POV: You Don't Just Watch, <span class='text-[var(--primary-accent)]'>You're the Creator.</span>"></h1>
                    <p class="text-lg md:text-xl max-w-3xl mx-auto text-[var(--text-secondary)] mb-10" data-lang-id="Kit elektronika buat lo yang curiga sama sihir. *Spoiler*: ini bukan sihir, ini sains yang keren abis." data-lang-en="Electronics kits for those who are suspicious of magic. *Spoiler*: it's not magic, it's just really cool science."></p>
                    <div class="flex flex-col sm:flex-row justify-center items-center gap-4">
                        <button onclick="showPage('kits-list-page')" class="btn-pro" data-lang-id="Jelajahi Proyek" data-lang-en="Explore Projects"></button>
                    </div>
                </section>
                
                <section id="how-it-works" class="py-24">
                    <h2 class="font-header text-3xl md:text-4xl text-center mb-12" data-lang-id="Gimana Caranya? Gampang." data-lang-en="How It Works? It's Easy."></h2>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
                        <div class="glass-card p-8 rounded-lg">
                            <i data-feather="package" class="w-16 h-16 mx-auto mb-4 text-[var(--primary-accent)]"></i>
                            <h3 class="font-tech-mono text-xl mb-2" data-lang-id="1. Pilih Proyek Lo" data-lang-en="1. Pick Your Project"></h3>
                            <p class="text-[var(--text-secondary)]" data-lang-id="Scroll katalog proyek nekat kami. Mau bikin robot anti-sosial atau stasiun cuaca? Semuanya ada." data-lang-en="Scroll our catalog of awesome projects. Want to build an anti-social robot or a weather station? We've got it."></p>
                        </div>
                        <div class="glass-card p-8 rounded-lg">
                            <i data-feather="key" class="w-16 h-16 mx-auto mb-4 text-[var(--secondary-accent)]"></i>
                            <h3 class="font-tech-mono text-xl mb-2" data-lang-id="2. Terima Amunisi" data-lang-en="2. Get The Ammunition"></h3>
                            <p class="text-[var(--text-secondary)]" data-lang-id="Kit dikirim ke markasmu, lengkap sama kode akses premium. Anggap aja ini *starter pack* lo." data-lang-en="The kit is deployed to your base, complete with a premium access code. Consider it your starter pack."></p>
                        </div>
                        <div class="glass-card p-8 rounded-lg">
                            <i data-feather="youtube" class="w-16 h-16 mx-auto mb-4 text-[var(--cta-accent)]"></i>
                            <h3 class="font-tech-mono text-xl mb-2" data-lang-id="3. Rakit & Pamer" data-lang-en="3. Build & Flex"></h3>
                            <p class="text-[var(--text-secondary)]" data-lang-id="Ikuti tutorial video yang *no-skip*. Rakit, oprek, gagal, ulangi. Kalau udah jadi, jangan lupa *flexing*." data-lang-en="Follow the no-skip video tutorials. Build, tinker, fail, repeat. When you're done, don't forget to flex."></p>
                        </div>
                    </div>
                </section>

                <section id="tutorial-section" class="py-24">
                    <h2 class="font-header text-3xl md:text-4xl text-center mb-12" data-lang-id="Ilmu Gratisan (Jangan Bilang Siapa-siapa)" data-lang-en="Free Knowledge (Don't Tell Anyone)"></h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" style="perspective: 1000px;">
                        <!-- Kartu Tutorial 1 (Gratis) -->
                        <div class="card-tilt glass-card rounded-xl overflow-hidden group border-2 border-transparent hover:border-primary-accent transition-all duration-300"><div class="relative border-4 border-[var(--text-secondary)] group-hover:border-primary-accent m-4 rounded-md overflow-hidden"><img src="https://placehold.co/600x400/120D21/EAEAEA?text=Video+Thumbnail" alt="[Thumbnail Video Tutorial]" class="w-full h-auto object-cover"><div class="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center"><svg class="w-16 h-16 text-white opacity-70 group-hover:opacity-100 group-hover:text-primary-accent transition-all" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path></svg></div></div><div class="p-6 pt-2"><h3 class="font-tech-mono text-xl mb-2">Video 1: Baca Suhu DHT11</h3><p class="text-[var(--text-secondary)] text-sm mb-4">Membaca data sensor suhu dan kelembaban paling populer.</p><div class="flex justify-between items-center"><span class="font-label text-xs py-1 px-3 rounded-full bg-[var(--primary-accent)] text-black" data-lang-id="Gratis" data-lang-en="Free"></span><button onclick="showPage('tutorial-preview-page', { title: 'Video 1: Baca Suhu DHT11', isPremium: false })" class="font-label text-sm text-[var(--primary-accent)] hover:text-white" data-lang-id="Pratinjau <i data-feather='arrow-right' class='inline w-4 h-4'></i>" data-lang-en="Preview <i data-feather='arrow-right' class='inline w-4 h-4'></i>"></button></div></div></div>
                        <!-- Kartu Tutorial 2 (Premium) -->
                        <div class="card-tilt glass-card rounded-xl overflow-hidden group border-2 border-transparent hover:border-primary-accent transition-all duration-300"><div class="relative border-4 border-[var(--text-secondary)] group-hover:border-primary-accent m-4 rounded-md overflow-hidden"><img src="https://placehold.co/600x400/120D21/EAEAEA?text=Video+Thumbnail" alt="[Thumbnail Video Tutorial]" class="w-full h-auto object-cover"><div class="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center"><svg class="w-16 h-16 text-white opacity-70 group-hover:opacity-100 group-hover:text-primary-accent transition-all" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path></svg></div></div><div class="p-6 pt-2"><h3 class="font-tech-mono text-xl mb-2">Video 2: Tampilkan ke OLED</h3><p class="text-[var(--text-secondary)] text-sm mb-4">Mengirim data sensor untuk ditampilkan di layar OLED SSD1306.</p><div class="flex justify-between items-center"><span class="font-label text-xs py-1 px-3 rounded-full bg-[var(--cta-accent)] text-black">Premium</span><button onclick="showPage('tutorial-preview-page', { title: 'Video 2: Tampilkan ke OLED', isPremium: true })" class="font-label text-sm text-[var(--primary-accent)] hover:text-white" data-lang-id="Pratinjau <i data-feather='arrow-right' class='inline w-4 h-4'></i>" data-lang-en="Preview <i data-feather='arrow-right' class='inline w-4 h-4'></i>"></button></div></div></div>
                        <!-- Kartu Tutorial 3 (Premium) -->
                        <div class="card-tilt glass-card rounded-xl overflow-hidden group border-2 border-transparent hover:border-primary-accent transition-all duration-300"><div class="relative border-4 border-[var(--text-secondary)] group-hover:border-primary-accent m-4 rounded-md overflow-hidden"><img src="https://placehold.co/600x400/120D21/EAEAEA?text=Video+Thumbnail" alt="[Thumbnail Video Tutorial]" class="w-full h-auto object-cover"><div class="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center"><svg class="w-16 h-16 text-white opacity-70 group-hover:opacity-100 group-hover:text-primary-accent transition-all" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path></svg></div></div><div class="p-6 pt-2"><h3 class="font-tech-mono text-xl mb-2">Video 3: Kontrol via WiFi</h3><p class="text-[var(--text-secondary)] text-sm mb-4">Membuat web server sederhana untuk menyalakan LED dari browser.</p><div class="flex justify-between items-center"><span class="font-label text-xs py-1 px-3 rounded-full bg-[var(--cta-accent)] text-black">Premium</span><button onclick="showPage('tutorial-preview-page', { title: 'Video 3: Kontrol via WiFi', isPremium: true })" class="font-label text-sm text-[var(--primary-accent)] hover:text-white" data-lang-id="Pratinjau <i data-feather='arrow-right' class='inline w-4 h-4'></i>" data-lang-en="Preview <i data-feather='arrow-right' class='inline w-4 h-4'></i>"></button></div></div></div>
                    </div>
                    <div class="text-center mt-12">
                        <button onclick="showPage('tutorials-list-page')" class="btn-pro" data-lang-id="Lihat Semua Tutorial" data-lang-en="View All Tutorials"></button>
                    </div>
                </section>
                <section id="kit-section" class="py-24"><h2 class="font-header text-3xl md:text-4xl text-center mb-12">[PROYEK_UNGGULAN]</h2>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-8" style="perspective: 1000px;">
                        <a href="#" onclick="event.preventDefault(); showPage('kit-detail-page', { from: 'landing-page' })" class="card-tilt z-10 glass-card p-6 rounded-lg text-center border border-gray-700 flex flex-col items-center justify-center hover:border-primary-accent transition-all duration-300"><h3 class="font-tech-mono text-2xl mb-2">Proyek: Robot Nolep</h3><p class="text-[var(--text-secondary)] font-label text-sm">Rakit robot yang jago ghosting tembok dan rintangan lain.</p></a>
                        <a href="#" onclick="event.preventDefault(); showPage('kit-detail-page', { from: 'landing-page' })" class="card-tilt z-10 glass-card p-6 rounded-lg text-center border border-gray-700 flex flex-col items-center justify-center hover:border-primary-accent transition-all duration-300"><h3 class="font-tech-mono text-2xl mb-2">Proyek: Stasiun Cuaca</h3><p class="text-[var(--text-secondary)] font-label text-sm">Biar lo bisa prediksi cuaca hati dia. Canda cuaca.</p></a>
                        <a href="#" onclick="event.preventDefault(); showPage('kit-detail-page', { from: 'landing-page' })" class="card-tilt z-10 glass-card p-6 rounded-lg text-center border border-gray-700 flex flex-col items-center justify-center hover:border-primary-accent transition-all duration-300"><h3 class="font-tech-mono text-2xl mb-2">Proyek: Lampu Disko</h3><p class="text-[var(--text-secondary)] font-label text-sm">Bikin kamar lo auto jadi tempat party dadakan.</p></a>
                    </div>
                     <div class="text-center mt-12">
                        <button onclick="showPage('kits-list-page')" class="btn-pro" data-lang-id="Lihat Semua Proyek" data-lang-en="View All Projects"></button>
                    </div>
                </section>
            </main>
            <footer id="contact-section" class="border-t-2 border-[var(--text-secondary)]/20 py-10">
                <div class="container mx-auto px-6 text-center font-label text-[var(--text-secondary)]">
                    <p class="text-[var(--primary-accent)] text-lg mb-4"><span class="text-[var(--secondary-accent)]">$</span> techno-rebel build complete</p>
                    <div class="flex justify-center space-x-6 mb-4"><a href="#" class="hover:text-primary-accent transition-colors">Instagram</a><span class="text-gray-700">/</span><a href="#" class="hover:text-primary-accent transition-colors">YouTube</a><span class="text-gray-700">/</span><a href="#" class="hover:text-primary-accent transition-colors">Threads</a></div>
                    <div class="flex justify-center items-center gap-4 mt-4 text-xs">
                        <span data-lang-id="Ganti Bahasa:" data-lang-en="Switch Language:"></span>
                        <button onclick="setLanguage('id')" class="font-bold hover:text-primary-accent">ID</button>
                        <span>|</span>
                        <button onclick="setLanguage('en')" class="font-bold hover:text-primary-accent">EN</button>
                    </div>
                </div>
            </footer>
        </div>

        <!-- ======================= HALAMAN LOGIN =========================== -->
        <div id="login-page" class="page min-h-screen flex-col">
            <header class="sticky top-0 z-50 glass-card">
                <nav class="container mx-auto px-6 py-4 flex justify-between items-center">
                    <a href="#" onclick="showPage('landing-page')" class="font-header text-2xl glitch-text" data-text="TECHNO-REBEL">TECHNO-REBEL</a>
                    <div class="hidden md:flex items-center space-x-8 font-label">
                        <a href="#" onclick="showPage('kits-list-page')" class="text-[var(--text-secondary)] hover:text-[var(--primary-accent)] transition-colors" data-lang-id="Etalase Proyek" data-lang-en="Project Catalog"></a>
                        <a href="#" onclick="showPage('tutorials-list-page')" class="text-[var(--text-secondary)] hover:text-[var(--primary-accent)] transition-colors" data-lang-id="Tutorial Hype" data-lang-en="Hype Tutorials"></a>
                    </div>
                    <a href="#" onclick="showPage('landing-page')" class="hidden md:block px-4 py-2 rounded-lg text-sm font-label border-2 border-[var(--text-secondary)] text-[var(--text-secondary)] hover:border-[var(--primary-accent)] hover:text-[var(--primary-accent)] transition-colors" data-lang-id="← Kembali" data-lang-en="← Back"></a>
                </nav>
            </header>
            <main class="container mx-auto px-6 py-16 flex-grow flex items-center justify-center">
                <div class="w-full max-w-md">
                    <div class="glass-card rounded-2xl p-8 border border-gray-700">
                        <div class="text-center mb-8">
                            <h1 class="font-tech-mono text-3xl text-[var(--primary-accent)]" data-lang-id="[LOGIN DULU, BIAR ASIK]" data-lang-en="[LOGIN, TO GET COOL]"></h1>
                            <p class="text-[var(--text-secondary)] font-label text-sm mt-2" data-lang-id="// Masukin kredensial lo, jangan sampe salah" data-lang-en="// Enter your credentials, don't mess it up"></p>
                        </div>
                        <form id="login-form" class="space-y-8">
                            <div class="input-group">
                                <input required type="email" name="email" autocomplete="off" class="input-pro" placeholder=" ">
                                <label class="input-label" data-lang-id="Email / Username" data-lang-en="Email / Username"></label>
                            </div>
                            <div class="input-group">
                                <input required type="password" name="password" autocomplete="off" class="input-pro" placeholder=" ">
                                <label class="input-label" data-lang-id="Password" data-lang-en="Password"></label>
                            </div>
                            <div class="pt-2">
                                <button type="submit" class="w-full btn-pro" data-lang-id="GASKEUN" data-lang-en="LET'S GO"></button>
                            </div>
                        </form>
                        <div class="text-center font-label text-sm mt-8">
                            <a href="#" onclick="showPage('forgot-password-page')" class="text-[var(--text-secondary)] hover:text-primary-accent transition-colors" data-lang-id="Lupa Password?" data-lang-en="Forgot Password?"></a>
                            <span class="text-gray-700 mx-2">|</span>
                            <a href="#" onclick="showPage('register-page')" class="text-[var(--text-secondary)] hover:text-primary-accent transition-colors" data-lang-id="Daftar Akun Baru" data-lang-en="Create New Account"></a>
                        </div>
                    </div>
                </div>
            </main>
        </div>
        
        <!-- ======================= HALAMAN REGISTRASI ====================== -->
        <div id="register-page" class="page min-h-screen flex-col">
            <header class="sticky top-0 z-50 glass-card">
                <nav class="container mx-auto px-6 py-4 flex justify-between items-center">
                    <a href="#" onclick="showPage('landing-page')" class="font-header text-2xl glitch-text" data-text="TECHNO-REBEL">TECHNO-REBEL</a>
                    <div class="hidden md:flex items-center space-x-8 font-label">
                        <a href="#" onclick="showPage('kits-list-page')" class="text-[var(--text-secondary)] hover:text-[var(--primary-accent)] transition-colors" data-lang-id="Etalase Proyek" data-lang-en="Project Catalog"></a>
                        <a href="#" onclick="showPage('tutorials-list-page')" class="text-[var(--text-secondary)] hover:text-[var(--primary-accent)] transition-colors" data-lang-id="Tutorial Hype" data-lang-en="Hype Tutorials"></a>
                    </div>
                    <a href="#" onclick="showPage('landing-page')" class="hidden md:block px-4 py-2 rounded-lg text-sm font-label border-2 border-[var(--text-secondary)] text-[var(--text-secondary)] hover:border-[var(--primary-accent)] hover:text-[var(--primary-accent)] transition-colors" data-lang-id="← Kembali" data-lang-en="← Back"></a>
                </nav>
            </header>
            <main class="container mx-auto px-6 py-16 flex-grow flex items-center justify-center">
                <div class="w-full max-w-md">
                    <div class="glass-card rounded-2xl p-8 border border-gray-700">
                        <div class="text-center mb-8">
                            <h1 class="font-tech-mono text-3xl text-[var(--primary-accent)]" data-lang-id="[GABUNG CIRCLE]" data-lang-en="[JOIN THE CIRCLE]"></h1>
                            <p class="text-[var(--text-secondary)] font-label text-sm mt-2" data-lang-id="// Selamat datang di pemberontakan" data-lang-en="// Welcome to the rebellion"></p>
                        </div>
                        <form id="register-form" class="space-y-8">
                            <div class="input-group">
                                <input required type="text" class="input-pro" placeholder=" ">
                                <label class="input-label" data-lang-id="Username" data-lang-en="Username"></label>
                            </div>
                            <div class="input-group">
                                <input required type="email" class="input-pro" placeholder=" ">
                                <label class="input-label" data-lang-id="Email" data-lang-en="Email"></label>
                            </div>
                            <div class="input-group">
                                <input required type="password" class="input-pro" placeholder=" ">
                                <label class="input-label" data-lang-id="Password" data-lang-en="Password"></label>
                            </div>
                            <div class="pt-2">
                                <button type="submit" class="w-full btn-pro" data-lang-id="BUAT AKUN" data-lang-en="CREATE ACCOUNT"></button>
                            </div>
                        </form>
                        <div class="text-center font-label text-sm mt-8">
                            <p class="text-[var(--text-secondary)]">
                                <span data-lang-id="Udah punya akun? " data-lang-en="Already have an account? "></span>
                                <a href="#" onclick="showPage('login-page')" class="text-primary-accent hover:text-white" data-lang-id="Login di sini" data-lang-en="Login here"></a>.
                            </p>
                        </div>
                    </div>
                </div>
            </main>
        </div>

        <!-- ======================= HALAMAN LUPA PASSWORD =================== -->
        <div id="forgot-password-page" class="page min-h-screen flex-col">
            <header class="sticky top-0 z-50 glass-card">
                <nav class="container mx-auto px-6 py-4 flex justify-between items-center">
                    <a href="#" onclick="showPage('landing-page')" class="font-header text-2xl glitch-text" data-text="TECHNO-REBEL">TECHNO-REBEL</a>
                    <div class="hidden md:flex items-center space-x-8 font-label">
                        <a href="#" onclick="showPage('kits-list-page')" class="text-[var(--text-secondary)] hover:text-[var(--primary-accent)] transition-colors" data-lang-id="Etalase Proyek" data-lang-en="Project Catalog"></a>
                        <a href="#" onclick="showPage('tutorials-list-page')" class="text-[var(--text-secondary)] hover:text-[var(--primary-accent)] transition-colors" data-lang-id="Tutorial Hype" data-lang-en="Hype Tutorials"></a>
                    </div>
                    <a href="#" onclick="showPage('landing-page')" class="hidden md:block px-4 py-2 rounded-lg text-sm font-label border-2 border-[var(--text-secondary)] text-[var(--text-secondary)] hover:border-[var(--primary-accent)] hover:text-[var(--primary-accent)] transition-colors" data-lang-id="← Kembali" data-lang-en="← Back"></a>
                </nav>
            </header>
            <main class="container mx-auto px-6 py-16 flex-grow flex items-center justify-center">
                <div class="w-full max-w-md">
                    <div class="glass-card rounded-2xl p-8 border border-gray-700">
                        <div class="text-center mb-8">
                            <h1 class="font-tech-mono text-3xl text-[var(--primary-accent)]">[RESET PASSWORD]</h1>
                            <p class="text-[var(--text-secondary)] font-label text-sm mt-2" data-lang-id="// Tenang, semua bisa di-debug" data-lang-en="// Don't worry, everything can be debugged"></p>
                        </div>
                        <form id="forgot-form" class="space-y-8">
                            <div class="input-group">
                                <input required type="email" class="input-pro" placeholder=" ">
                                <label class="input-label" data-lang-id="Alamat Email" data-lang-en="Email Address"></label>
                            </div>
                            <div class="pt-2">
                                <button type="submit" class="w-full btn-pro" data-lang-id="KIRIM LINK RESET" data-lang-en="SEND RESET LINK"></button>
                            </div>
                        </form>
                        <div class="text-center font-label text-sm mt-8">
                            <a href="#" onclick="showPage('login-page')" class="text-[var(--text-secondary)] hover:text-primary-accent transition-colors" data-lang-id="Kembali ke Login" data-lang-en="Back to Login"></a>
                            <span class="text-gray-700 mx-2">|</span>
                            <a href="#" onclick="showPage('landing-page')" class="text-[var(--text-secondary)] hover:text-primary-accent transition-colors" data-lang-id="Ke Beranda" data-lang-en="To Homepage"></a>
                        </div>
                    </div>
                </div>
            </main>
        </div>

        <!-- ======================= HALAMAN ALL PROJECTS ======================= -->
        <div id="kits-list-page" class="page min-h-screen flex-col">
            <header class="sticky top-0 z-50 glass-card">
                <nav class="container mx-auto px-6 py-4 flex justify-between items-center">
                    <a href="#" onclick="showPage('landing-page')" class="font-header text-2xl glitch-text" data-text="TECHNO-REBEL">TECHNO-REBEL</a>
                    <div class="hidden md:flex items-center space-x-8 font-label">
                        <a href="#" onclick="showPage('kits-list-page')" class="text-[var(--primary-accent)] border-b-2 border-[var(--primary-accent)]" data-lang-id="Etalase Proyek" data-lang-en="Project Catalog"></a>
                        <a href="#" onclick="showPage('tutorials-list-page')" class="text-[var(--text-secondary)] hover:text-[var(--primary-accent)] transition-colors" data-lang-id="Tutorial Hype" data-lang-en="Hype Tutorials"></a>
                    </div>
                    <a href="#" onclick="showPage('login-page')" class="hidden md:block px-4 py-2 rounded-lg text-sm font-label border-2 border-[var(--primary-accent)] text-[var(--primary-accent)] hover:bg-[var(--primary-accent)] hover:text-black transition-colors" data-lang-id="LOGIN" data-lang-en="LOGIN"></a>
                </nav>
            </header>

            <main class="container mx-auto px-6 py-16">
                <!-- Breadcrumb -->
                <nav class="flex mb-8 font-label text-sm">
                    <a href="#" onclick="showPage('landing-page')" class="text-[var(--text-secondary)] hover:text-[var(--primary-accent)]" data-lang-id="Beranda" data-lang-en="Home"></a>
                    <span class="mx-2 text-[var(--text-secondary)]">/</span>
                    <span class="text-[var(--primary-accent)]" data-lang-id="Etalase Proyek" data-lang-en="Project Catalog"></span>
                </nav>

                <!-- Header Section -->
                <div class="text-center mb-12">
                    <h1 class="font-header text-4xl md:text-5xl mb-4" data-lang-id="[ETALASE_PROYEK]" data-lang-en="[PROJECT_CATALOG]"></h1>
                    <p class="text-lg text-[var(--text-secondary)] max-w-2xl mx-auto" data-lang-id="Koleksi lengkap kit elektronika untuk semua level. Dari pemula sampai yang udah jago banget." data-lang-en="Complete collection of electronics kits for all levels. From beginners to advanced pros."></p>
                </div>

                <!-- Search and Filter -->
                <div class="glass-card rounded-lg p-6 mb-8">
                    <div class="flex flex-col md:flex-row gap-4">
                        <div class="input-group flex-1">
                            <input type="text" id="project-search" class="input-pro" placeholder=" " onkeyup="filterProjects()">
                            <label class="input-label" data-lang-id="Cari proyek..." data-lang-en="Search projects..."></label>
                        </div>
                        <div class="input-group">
                            <select id="project-filter" class="input-pro" onchange="filterProjects()">
                                <option value="all" data-lang-id="Semua Kategori" data-lang-en="All Categories">Semua Kategori</option>
                                <option value="beginner" data-lang-id="Pemula" data-lang-en="Beginner">Pemula</option>
                                <option value="intermediate" data-lang-id="Menengah" data-lang-en="Intermediate">Menengah</option>
                                <option value="advanced" data-lang-id="Lanjutan" data-lang-en="Advanced">Lanjutan</option>
                            </select>
                        </div>
                        <div class="input-group">
                            <select id="price-filter" class="input-pro" onchange="filterProjects()">
                                <option value="all" data-lang-id="Semua Harga" data-lang-en="All Prices">Semua Harga</option>
                                <option value="low">< Rp 300.000</option>
                                <option value="medium">Rp 300.000 - 500.000</option>
                                <option value="high">> Rp 500.000</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Projects Grid -->
                <div id="projects-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" style="perspective: 1000px;">
                    <!-- Project Card 1 -->
                    <div class="project-card card-tilt glass-card rounded-xl overflow-hidden group border-2 border-transparent hover:border-primary-accent transition-all duration-300" data-category="beginner" data-price="350000">
                        <div class="relative border-4 border-[var(--text-secondary)] group-hover:border-primary-accent m-4 rounded-md overflow-hidden">
                            <img src="https://placehold.co/400x300/120D21/00F5D4?text=Robot+Nolep" alt="Robot Nolep Kit" class="w-full h-48 object-cover">
                            <div class="absolute top-2 right-2">
                                <span class="bg-[var(--primary-accent)] text-black px-2 py-1 rounded-full text-xs font-label">Pemula</span>
                            </div>
                        </div>
                        <div class="p-6 pt-2">
                            <h3 class="font-tech-mono text-xl mb-2">Proyek: Robot Nolep</h3>
                            <p class="text-[var(--text-secondary)] text-sm mb-4">Rakit robot yang jago ghosting tembok dan rintangan lain. Perfect untuk pemula yang mau langsung liat hasil.</p>
                            <div class="flex justify-between items-center mb-4">
                                <span class="font-header text-xl text-[var(--primary-accent)]">Rp 350.000</span>
                                <div class="flex items-center gap-1">
                                    <i data-feather="star" class="w-4 h-4 text-[var(--cta-accent)] fill-current"></i>
                                    <span class="text-sm">4.8</span>
                                </div>
                            </div>
                            <div class="flex gap-2">
                                <button onclick="showPage('kit-detail-page', { from: 'kits-list-page', kitId: 'robot-nolep' })" class="flex-1 btn-pro text-sm py-2" data-lang-id="Detail" data-lang-en="Details">Detail</button>
                                <button onclick="buyKit('robot-nolep')" class="px-3 py-2 rounded-lg text-sm border border-[var(--primary-accent)] text-[var(--primary-accent)] hover:bg-[var(--primary-accent)] hover:text-black transition-colors">
                                    <i data-feather="shopping-cart" class="w-4 h-4"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Project Card 2 -->
                    <div class="project-card card-tilt glass-card rounded-xl overflow-hidden group border-2 border-transparent hover:border-primary-accent transition-all duration-300" data-category="intermediate" data-price="450000">
                        <div class="relative border-4 border-[var(--text-secondary)] group-hover:border-primary-accent m-4 rounded-md overflow-hidden">
                            <img src="https://placehold.co/400x300/120D21/9B5DE5?text=Stasiun+Cuaca" alt="Weather Station Kit" class="w-full h-48 object-cover">
                            <div class="absolute top-2 right-2">
                                <span class="bg-[var(--secondary-accent)] text-white px-2 py-1 rounded-full text-xs font-label">Menengah</span>
                            </div>
                        </div>
                        <div class="p-6 pt-2">
                            <h3 class="font-tech-mono text-xl mb-2">Proyek: Stasiun Cuaca</h3>
                            <p class="text-[var(--text-secondary)] text-sm mb-4">Monitor suhu, kelembaban, dan tekanan udara real-time. Data bisa dilihat via web dashboard.</p>
                            <div class="flex justify-between items-center mb-4">
                                <span class="font-header text-xl text-[var(--primary-accent)]">Rp 450.000</span>
                                <div class="flex items-center gap-1">
                                    <i data-feather="star" class="w-4 h-4 text-[var(--cta-accent)] fill-current"></i>
                                    <span class="text-sm">4.9</span>
                                </div>
                            </div>
                            <div class="flex gap-2">
                                <button onclick="showPage('kit-detail-page', { from: 'kits-list-page', kitId: 'weather-station' })" class="flex-1 btn-pro text-sm py-2" data-lang-id="Detail" data-lang-en="Details">Detail</button>
                                <button onclick="buyKit('weather-station')" class="px-3 py-2 rounded-lg text-sm border border-[var(--primary-accent)] text-[var(--primary-accent)] hover:bg-[var(--primary-accent)] hover:text-black transition-colors">
                                    <i data-feather="shopping-cart" class="w-4 h-4"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Project Card 3 -->
                    <div class="project-card card-tilt glass-card rounded-xl overflow-hidden group border-2 border-transparent hover:border-primary-accent transition-all duration-300" data-category="beginner" data-price="275000">
                        <div class="relative border-4 border-[var(--text-secondary)] group-hover:border-primary-accent m-4 rounded-md overflow-hidden">
                            <img src="https://placehold.co/400x300/120D21/FEE440?text=Lampu+Disko" alt="Disco Light Kit" class="w-full h-48 object-cover">
                            <div class="absolute top-2 right-2">
                                <span class="bg-[var(--primary-accent)] text-black px-2 py-1 rounded-full text-xs font-label">Pemula</span>
                            </div>
                        </div>
                        <div class="p-6 pt-2">
                            <h3 class="font-tech-mono text-xl mb-2">Proyek: Lampu Disko</h3>
                            <p class="text-[var(--text-secondary)] text-sm mb-4">Bikin kamar lo auto jadi tempat party dadakan. LED strip yang sync sama musik.</p>
                            <div class="flex justify-between items-center mb-4">
                                <span class="font-header text-xl text-[var(--primary-accent)]">Rp 275.000</span>
                                <div class="flex items-center gap-1">
                                    <i data-feather="star" class="w-4 h-4 text-[var(--cta-accent)] fill-current"></i>
                                    <span class="text-sm">4.7</span>
                                </div>
                            </div>
                            <button onclick="showPage('kit-detail-page', { from: 'kits-list-page', kitId: 'disco-light' })" class="w-full btn-pro text-sm py-2" data-lang-id="Lihat Detail" data-lang-en="View Details"></button>
                        </div>
                    </div>

                    <!-- Project Card 4 -->
                    <div class="project-card card-tilt glass-card rounded-xl overflow-hidden group border-2 border-transparent hover:border-primary-accent transition-all duration-300" data-category="advanced" data-price="650000">
                        <div class="relative border-4 border-[var(--text-secondary)] group-hover:border-primary-accent m-4 rounded-md overflow-hidden">
                            <img src="https://placehold.co/400x300/120D21/00F5D4?text=IoT+Security" alt="IoT Security Kit" class="w-full h-48 object-cover">
                            <div class="absolute top-2 right-2">
                                <span class="bg-[var(--cta-accent)] text-black px-2 py-1 rounded-full text-xs font-label">Lanjutan</span>
                            </div>
                        </div>
                        <div class="p-6 pt-2">
                            <h3 class="font-tech-mono text-xl mb-2">Proyek: IoT Security</h3>
                            <p class="text-[var(--text-secondary)] text-sm mb-4">Sistem keamanan pintar dengan kamera, sensor gerak, dan notifikasi real-time ke smartphone.</p>
                            <div class="flex justify-between items-center mb-4">
                                <span class="font-header text-xl text-[var(--primary-accent)]">Rp 650.000</span>
                                <div class="flex items-center gap-1">
                                    <i data-feather="star" class="w-4 h-4 text-[var(--cta-accent)] fill-current"></i>
                                    <span class="text-sm">5.0</span>
                                </div>
                            </div>
                            <button onclick="showPage('kit-detail-page', { from: 'kits-list-page', kitId: 'iot-security' })" class="w-full btn-pro text-sm py-2" data-lang-id="Lihat Detail" data-lang-en="View Details"></button>
                        </div>
                    </div>

                    <!-- Project Card 5 -->
                    <div class="project-card card-tilt glass-card rounded-xl overflow-hidden group border-2 border-transparent hover:border-primary-accent transition-all duration-300" data-category="intermediate" data-price="525000">
                        <div class="relative border-4 border-[var(--text-secondary)] group-hover:border-primary-accent m-4 rounded-md overflow-hidden">
                            <img src="https://placehold.co/400x300/120D21/9B5DE5?text=Smart+Garden" alt="Smart Garden Kit" class="w-full h-48 object-cover">
                            <div class="absolute top-2 right-2">
                                <span class="bg-[var(--secondary-accent)] text-white px-2 py-1 rounded-full text-xs font-label">Menengah</span>
                            </div>
                        </div>
                        <div class="p-6 pt-2">
                            <h3 class="font-tech-mono text-xl mb-2">Proyek: Smart Garden</h3>
                            <p class="text-[var(--text-secondary)] text-sm mb-4">Otomatis siram tanaman berdasarkan kelembaban tanah. Perfect untuk yang suka lupa nyiram.</p>
                            <div class="flex justify-between items-center mb-4">
                                <span class="font-header text-xl text-[var(--primary-accent)]">Rp 525.000</span>
                                <div class="flex items-center gap-1">
                                    <i data-feather="star" class="w-4 h-4 text-[var(--cta-accent)] fill-current"></i>
                                    <span class="text-sm">4.6</span>
                                </div>
                            </div>
                            <button onclick="showPage('kit-detail-page', { from: 'kits-list-page', kitId: 'smart-garden' })" class="w-full btn-pro text-sm py-2" data-lang-id="Lihat Detail" data-lang-en="View Details"></button>
                        </div>
                    </div>

                    <!-- Project Card 6 -->
                    <div class="project-card card-tilt glass-card rounded-xl overflow-hidden group border-2 border-transparent hover:border-primary-accent transition-all duration-300" data-category="beginner" data-price="225000">
                        <div class="relative border-4 border-[var(--text-secondary)] group-hover:border-primary-accent m-4 rounded-md overflow-hidden">
                            <img src="https://placehold.co/400x300/120D21/FEE440?text=LED+Matrix" alt="LED Matrix Kit" class="w-full h-48 object-cover">
                            <div class="absolute top-2 right-2">
                                <span class="bg-[var(--primary-accent)] text-black px-2 py-1 rounded-full text-xs font-label">Pemula</span>
                            </div>
                        </div>
                        <div class="p-6 pt-2">
                            <h3 class="font-tech-mono text-xl mb-2">Proyek: LED Matrix</h3>
                            <p class="text-[var(--text-secondary)] text-sm mb-4">Tampilkan teks dan animasi keren di matrix LED 8x8. Cocok untuk belajar dasar programming.</p>
                            <div class="flex justify-between items-center mb-4">
                                <span class="font-header text-xl text-[var(--primary-accent)]">Rp 225.000</span>
                                <div class="flex items-center gap-1">
                                    <i data-feather="star" class="w-4 h-4 text-[var(--cta-accent)] fill-current"></i>
                                    <span class="text-sm">4.5</span>
                                </div>
                            </div>
                            <button onclick="showPage('kit-detail-page', { from: 'kits-list-page', kitId: 'led-matrix' })" class="w-full btn-pro text-sm py-2" data-lang-id="Lihat Detail" data-lang-en="View Details"></button>
                        </div>
                    </div>
                </div>

                <!-- No Results Message -->
                <div id="no-results" class="text-center py-16 hidden">
                    <i data-feather="search" class="w-16 h-16 mx-auto mb-4 text-[var(--text-secondary)]"></i>
                    <h3 class="font-tech-mono text-xl mb-2" data-lang-id="Tidak ada proyek yang ditemukan" data-lang-en="No projects found"></h3>
                    <p class="text-[var(--text-secondary)]" data-lang-id="Coba ubah filter atau kata kunci pencarian" data-lang-en="Try changing the filter or search keywords"></p>
                </div>
            </main>

            <footer class="border-t-2 border-[var(--text-secondary)]/20 py-10">
                <div class="container mx-auto px-6 text-center font-label text-[var(--text-secondary)]">
                    <p class="text-[var(--primary-accent)] text-lg mb-4"><span class="text-[var(--secondary-accent)]">$</span> techno-rebel build complete</p>
                    <div class="flex justify-center space-x-6 mb-4">
                        <a href="#" class="hover:text-primary-accent transition-colors">Instagram</a>
                        <span class="text-gray-700">/</span>
                        <a href="#" class="hover:text-primary-accent transition-colors">YouTube</a>
                        <span class="text-gray-700">/</span>
                        <a href="#" class="hover:text-primary-accent transition-colors">Threads</a>
                    </div>
                </div>
            </footer>
        </div>

        <!-- ======================= HALAMAN ALL TUTORIALS ======================= -->
        <div id="tutorials-list-page" class="page min-h-screen flex-col">
            <header class="sticky top-0 z-50 glass-card">
                <nav class="container mx-auto px-6 py-4 flex justify-between items-center">
                    <a href="#" onclick="showPage('landing-page')" class="font-header text-2xl glitch-text" data-text="TECHNO-REBEL">TECHNO-REBEL</a>
                    <div class="hidden md:flex items-center space-x-8 font-label">
                        <a href="#" onclick="showPage('kits-list-page')" class="text-[var(--text-secondary)] hover:text-[var(--primary-accent)] transition-colors" data-lang-id="Etalase Proyek" data-lang-en="Project Catalog"></a>
                        <a href="#" onclick="showPage('tutorials-list-page')" class="text-[var(--primary-accent)] border-b-2 border-[var(--primary-accent)]" data-lang-id="Tutorial Hype" data-lang-en="Hype Tutorials"></a>
                    </div>
                    <a href="#" onclick="showPage('login-page')" class="hidden md:block px-4 py-2 rounded-lg text-sm font-label border-2 border-[var(--primary-accent)] text-[var(--primary-accent)] hover:bg-[var(--primary-accent)] hover:text-black transition-colors" data-lang-id="LOGIN" data-lang-en="LOGIN"></a>
                </nav>
            </header>

            <main class="container mx-auto px-6 py-16">
                <!-- Breadcrumb -->
                <nav class="flex mb-8 font-label text-sm">
                    <a href="#" onclick="showPage('landing-page')" class="text-[var(--text-secondary)] hover:text-[var(--primary-accent)]" data-lang-id="Beranda" data-lang-en="Home"></a>
                    <span class="mx-2 text-[var(--text-secondary)]">/</span>
                    <span class="text-[var(--primary-accent)]" data-lang-id="Tutorial Hype" data-lang-en="Hype Tutorials"></span>
                </nav>

                <!-- Header Section -->
                <div class="text-center mb-12">
                    <h1 class="font-header text-4xl md:text-5xl mb-4" data-lang-id="[TUTORIAL_HYPE]" data-lang-en="[HYPE_TUTORIALS]"></h1>
                    <p class="text-lg text-[var(--text-secondary)] max-w-2xl mx-auto" data-lang-id="Ilmu gratisan dan premium untuk naik level. Dari basic sampai yang bikin kepala pusing." data-lang-en="Free and premium knowledge to level up. From basics to mind-bending stuff."></p>
                </div>

                <!-- Filter Tabs -->
                <div class="flex justify-center mb-8">
                    <div class="glass-card rounded-lg p-2 inline-flex">
                        <button onclick="filterTutorials('all')" id="filter-all" class="tutorial-filter-btn px-6 py-2 rounded-md font-label text-sm transition-all bg-[var(--primary-accent)] text-black" data-lang-id="Semua" data-lang-en="All">Semua</button>
                        <button onclick="filterTutorials('free')" id="filter-free" class="tutorial-filter-btn px-6 py-2 rounded-md font-label text-sm transition-all text-[var(--text-secondary)] hover:text-[var(--primary-accent)]" data-lang-id="Gratis" data-lang-en="Free">Gratis</button>
                        <button onclick="filterTutorials('premium')" id="filter-premium" class="tutorial-filter-btn px-6 py-2 rounded-md font-label text-sm transition-all text-[var(--text-secondary)] hover:text-[var(--primary-accent)]" data-lang-id="Premium" data-lang-en="Premium">Premium</button>
                    </div>
                </div>

                <!-- Search Bar -->
                <div class="glass-card rounded-lg p-6 mb-8">
                    <div class="input-group max-w-md mx-auto">
                        <input type="text" id="tutorial-search" class="input-pro" placeholder=" " onkeyup="searchTutorials()">
                        <label class="input-label" data-lang-id="Cari tutorial..." data-lang-en="Search tutorials..."></label>
                    </div>
                </div>

                <!-- Tutorials Grid -->
                <div id="tutorials-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" style="perspective: 1000px;">
                    <!-- Tutorial Card 1 - Free -->
                    <div class="tutorial-card card-tilt glass-card rounded-xl overflow-hidden group border-2 border-transparent hover:border-primary-accent transition-all duration-300" data-type="free">
                        <div class="relative border-4 border-[var(--text-secondary)] group-hover:border-primary-accent m-4 rounded-md overflow-hidden">
                            <img src="https://placehold.co/600x400/120D21/EAEAEA?text=DHT11+Tutorial" alt="DHT11 Tutorial Thumbnail" class="w-full h-48 object-cover">
                            <div class="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center">
                                <svg class="w-16 h-16 text-white opacity-70 group-hover:opacity-100 group-hover:text-primary-accent transition-all" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div class="absolute top-2 right-2">
                                <span class="bg-[var(--primary-accent)] text-black px-2 py-1 rounded-full text-xs font-label" data-lang-id="Gratis" data-lang-en="Free">Gratis</span>
                            </div>
                        </div>
                        <div class="p-6 pt-2">
                            <h3 class="font-tech-mono text-xl mb-2">Video 1: Baca Suhu DHT11</h3>
                            <p class="text-[var(--text-secondary)] text-sm mb-4">Membaca data sensor suhu dan kelembaban paling populer. Perfect untuk pemula yang mau mulai IoT.</p>
                            <div class="flex justify-between items-center mb-4">
                                <div class="flex items-center gap-2">
                                    <i data-feather="clock" class="w-4 h-4 text-[var(--text-secondary)]"></i>
                                    <span class="text-sm text-[var(--text-secondary)]">15 menit</span>
                                </div>
                                <div class="flex items-center gap-1">
                                    <i data-feather="star" class="w-4 h-4 text-[var(--cta-accent)] fill-current"></i>
                                    <span class="text-sm">4.8</span>
                                </div>
                            </div>
                            <button onclick="showPage('tutorial-preview-page', { title: 'Video 1: Baca Suhu DHT11', isPremium: false })" class="w-full btn-pro text-sm py-2" data-lang-id="Tonton Sekarang" data-lang-en="Watch Now"></button>
                        </div>
                    </div>

                    <!-- Tutorial Card 2 - Premium -->
                    <div class="tutorial-card card-tilt glass-card rounded-xl overflow-hidden group border-2 border-transparent hover:border-primary-accent transition-all duration-300" data-type="premium">
                        <div class="relative border-4 border-[var(--text-secondary)] group-hover:border-primary-accent m-4 rounded-md overflow-hidden">
                            <img src="https://placehold.co/600x400/120D21/EAEAEA?text=OLED+Display" alt="OLED Display Tutorial" class="w-full h-48 object-cover">
                            <div class="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center">
                                <svg class="w-16 h-16 text-white opacity-70 group-hover:opacity-100 group-hover:text-primary-accent transition-all" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div class="absolute top-2 right-2">
                                <span class="bg-[var(--cta-accent)] text-black px-2 py-1 rounded-full text-xs font-label">Premium</span>
                            </div>
                        </div>
                        <div class="p-6 pt-2">
                            <h3 class="font-tech-mono text-xl mb-2">Video 2: Tampilkan ke OLED</h3>
                            <p class="text-[var(--text-secondary)] text-sm mb-4">Mengirim data sensor untuk ditampilkan di layar OLED SSD1306. Bikin project lo lebih interaktif.</p>

                            <!-- Kit Terkait -->
                            <div class="bg-gray-800/50 rounded-lg p-3 mb-4">
                                <div class="flex items-center gap-2 mb-2">
                                    <i data-feather="package" class="w-4 h-4 text-[var(--primary-accent)]"></i>
                                    <span class="text-xs text-[var(--primary-accent)] font-label">Kit Terkait:</span>
                                </div>
                                <div class="flex items-center gap-2">
                                    <img src="https://placehold.co/30x30/120D21/00F5D4?text=ESP" alt="ESP32 Kit" class="rounded border border-[var(--primary-accent)]">
                                    <div class="flex-1">
                                        <p class="text-xs font-label">Robot Nolep Kit</p>
                                        <p class="text-xs text-[var(--cta-accent)]">Beli kit → Gratis tutorial premium!</p>
                                    </div>
                                </div>
                            </div>

                            <div class="flex justify-between items-center mb-4">
                                <div class="flex items-center gap-2">
                                    <i data-feather="clock" class="w-4 h-4 text-[var(--text-secondary)]"></i>
                                    <span class="text-sm text-[var(--text-secondary)]">22 menit</span>
                                </div>
                                <div class="flex items-center gap-1">
                                    <i data-feather="star" class="w-4 h-4 text-[var(--cta-accent)] fill-current"></i>
                                    <span class="text-sm">4.9</span>
                                </div>
                            </div>
                            <button onclick="showPage('tutorial-preview-page', { title: 'Video 2: Tampilkan ke OLED', isPremium: true })" class="w-full btn-pro text-sm py-2" data-lang-id="Pratinjau" data-lang-en="Preview"></button>
                        </div>
                    </div>

                    <!-- Tutorial Card 3 - Premium -->
                    <div class="tutorial-card card-tilt glass-card rounded-xl overflow-hidden group border-2 border-transparent hover:border-primary-accent transition-all duration-300" data-type="premium">
                        <div class="relative border-4 border-[var(--text-secondary)] group-hover:border-primary-accent m-4 rounded-md overflow-hidden">
                            <img src="https://placehold.co/600x400/120D21/EAEAEA?text=WiFi+Control" alt="WiFi Control Tutorial" class="w-full h-48 object-cover">
                            <div class="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center">
                                <svg class="w-16 h-16 text-white opacity-70 group-hover:opacity-100 group-hover:text-primary-accent transition-all" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div class="absolute top-2 right-2">
                                <span class="bg-[var(--cta-accent)] text-black px-2 py-1 rounded-full text-xs font-label">Premium</span>
                            </div>
                        </div>
                        <div class="p-6 pt-2">
                            <h3 class="font-tech-mono text-xl mb-2">Video 3: Kontrol via WiFi</h3>
                            <p class="text-[var(--text-secondary)] text-sm mb-4">Membuat web server sederhana untuk menyalakan LED dari browser. Remote control level dewa.</p>
                            <div class="flex justify-between items-center mb-4">
                                <div class="flex items-center gap-2">
                                    <i data-feather="clock" class="w-4 h-4 text-[var(--text-secondary)]"></i>
                                    <span class="text-sm text-[var(--text-secondary)]">28 menit</span>
                                </div>
                                <div class="flex items-center gap-1">
                                    <i data-feather="star" class="w-4 h-4 text-[var(--cta-accent)] fill-current"></i>
                                    <span class="text-sm">5.0</span>
                                </div>
                            </div>
                            <button onclick="showPage('tutorial-preview-page', { title: 'Video 3: Kontrol via WiFi', isPremium: true })" class="w-full btn-pro text-sm py-2" data-lang-id="Pratinjau" data-lang-en="Preview"></button>
                        </div>
                    </div>

                    <!-- Tutorial Card 4 - Free -->
                    <div class="tutorial-card card-tilt glass-card rounded-xl overflow-hidden group border-2 border-transparent hover:border-primary-accent transition-all duration-300" data-type="free">
                        <div class="relative border-4 border-[var(--text-secondary)] group-hover:border-primary-accent m-4 rounded-md overflow-hidden">
                            <img src="https://placehold.co/600x400/120D21/EAEAEA?text=LED+Basics" alt="LED Basics Tutorial" class="w-full h-48 object-cover">
                            <div class="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center">
                                <svg class="w-16 h-16 text-white opacity-70 group-hover:opacity-100 group-hover:text-primary-accent transition-all" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div class="absolute top-2 right-2">
                                <span class="bg-[var(--primary-accent)] text-black px-2 py-1 rounded-full text-xs font-label" data-lang-id="Gratis" data-lang-en="Free">Gratis</span>
                            </div>
                        </div>
                        <div class="p-6 pt-2">
                            <h3 class="font-tech-mono text-xl mb-2">Video 0: LED Basics</h3>
                            <p class="text-[var(--text-secondary)] text-sm mb-4">Dasar-dasar LED dan resistor. Mulai dari sini kalau lo bener-bener newbie di elektronika.</p>
                            <div class="flex justify-between items-center mb-4">
                                <div class="flex items-center gap-2">
                                    <i data-feather="clock" class="w-4 h-4 text-[var(--text-secondary)]"></i>
                                    <span class="text-sm text-[var(--text-secondary)]">12 menit</span>
                                </div>
                                <div class="flex items-center gap-1">
                                    <i data-feather="star" class="w-4 h-4 text-[var(--cta-accent)] fill-current"></i>
                                    <span class="text-sm">4.6</span>
                                </div>
                            </div>
                            <button onclick="showPage('tutorial-preview-page', { title: 'Video 0: LED Basics', isPremium: false })" class="w-full btn-pro text-sm py-2" data-lang-id="Tonton Sekarang" data-lang-en="Watch Now"></button>
                        </div>
                    </div>

                    <!-- Tutorial Card 5 - Premium -->
                    <div class="tutorial-card card-tilt glass-card rounded-xl overflow-hidden group border-2 border-transparent hover:border-primary-accent transition-all duration-300" data-type="premium">
                        <div class="relative border-4 border-[var(--text-secondary)] group-hover:border-primary-accent m-4 rounded-md overflow-hidden">
                            <img src="https://placehold.co/600x400/120D21/EAEAEA?text=Database+IoT" alt="Database IoT Tutorial" class="w-full h-48 object-cover">
                            <div class="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center">
                                <svg class="w-16 h-16 text-white opacity-70 group-hover:opacity-100 group-hover:text-primary-accent transition-all" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div class="absolute top-2 right-2">
                                <span class="bg-[var(--cta-accent)] text-black px-2 py-1 rounded-full text-xs font-label">Premium</span>
                            </div>
                        </div>
                        <div class="p-6 pt-2">
                            <h3 class="font-tech-mono text-xl mb-2">Video 4: Kirim Data ke Server</h3>
                            <p class="text-[var(--text-secondary)] text-sm mb-4">Upload data sensor ke database cloud. Bikin dashboard web untuk monitoring real-time.</p>
                            <div class="flex justify-between items-center mb-4">
                                <div class="flex items-center gap-2">
                                    <i data-feather="clock" class="w-4 h-4 text-[var(--text-secondary)]"></i>
                                    <span class="text-sm text-[var(--text-secondary)]">35 menit</span>
                                </div>
                                <div class="flex items-center gap-1">
                                    <i data-feather="star" class="w-4 h-4 text-[var(--cta-accent)] fill-current"></i>
                                    <span class="text-sm">4.9</span>
                                </div>
                            </div>
                            <button onclick="showPage('tutorial-preview-page', { title: 'Video 4: Kirim Data ke Server', isPremium: true })" class="w-full btn-pro text-sm py-2" data-lang-id="Pratinjau" data-lang-en="Preview"></button>
                        </div>
                    </div>

                    <!-- Tutorial Card 6 - Free -->
                    <div class="tutorial-card card-tilt glass-card rounded-xl overflow-hidden group border-2 border-transparent hover:border-primary-accent transition-all duration-300" data-type="free">
                        <div class="relative border-4 border-[var(--text-secondary)] group-hover:border-primary-accent m-4 rounded-md overflow-hidden">
                            <img src="https://placehold.co/600x400/120D21/EAEAEA?text=Arduino+IDE" alt="Arduino IDE Tutorial" class="w-full h-48 object-cover">
                            <div class="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center">
                                <svg class="w-16 h-16 text-white opacity-70 group-hover:opacity-100 group-hover:text-primary-accent transition-all" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div class="absolute top-2 right-2">
                                <span class="bg-[var(--primary-accent)] text-black px-2 py-1 rounded-full text-xs font-label" data-lang-id="Gratis" data-lang-en="Free">Gratis</span>
                            </div>
                        </div>
                        <div class="p-6 pt-2">
                            <h3 class="font-tech-mono text-xl mb-2">Setup: Arduino IDE</h3>
                            <p class="text-[var(--text-secondary)] text-sm mb-4">Install dan setup Arduino IDE untuk ESP32. Wajib ditonton sebelum mulai coding.</p>
                            <div class="flex justify-between items-center mb-4">
                                <div class="flex items-center gap-2">
                                    <i data-feather="clock" class="w-4 h-4 text-[var(--text-secondary)]"></i>
                                    <span class="text-sm text-[var(--text-secondary)]">18 menit</span>
                                </div>
                                <div class="flex items-center gap-1">
                                    <i data-feather="star" class="w-4 h-4 text-[var(--cta-accent)] fill-current"></i>
                                    <span class="text-sm">4.7</span>
                                </div>
                            </div>
                            <button onclick="showPage('tutorial-preview-page', { title: 'Setup: Arduino IDE', isPremium: false })" class="w-full btn-pro text-sm py-2" data-lang-id="Tonton Sekarang" data-lang-en="Watch Now"></button>
                        </div>
                    </div>
                </div>

                <!-- No Results Message -->
                <div id="no-tutorial-results" class="text-center py-16 hidden">
                    <i data-feather="search" class="w-16 h-16 mx-auto mb-4 text-[var(--text-secondary)]"></i>
                    <h3 class="font-tech-mono text-xl mb-2" data-lang-id="Tidak ada tutorial yang ditemukan" data-lang-en="No tutorials found"></h3>
                    <p class="text-[var(--text-secondary)]" data-lang-id="Coba ubah filter atau kata kunci pencarian" data-lang-en="Try changing the filter or search keywords"></p>
                </div>

                <!-- CTA Section -->
                <div class="glass-card rounded-xl p-8 mt-16 text-center">
                    <h2 class="font-header text-3xl mb-4" data-lang-id="Mau Akses Semua Tutorial Premium?" data-lang-en="Want Access to All Premium Tutorials?"></h2>
                    <p class="text-[var(--text-secondary)] mb-6 max-w-2xl mx-auto" data-lang-id="Beli kit apapun dan dapatkan kode akses premium gratis. Atau beli kode akses terpisah via WhatsApp." data-lang-en="Buy any kit and get free premium access code. Or buy access code separately via WhatsApp."></p>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <button onclick="showPage('kits-list-page')" class="btn-pro" data-lang-id="Lihat Kit" data-lang-en="View Kits"></button>
                        <button onclick="buyPremiumCode()" class="btn-pro" style="--primary-accent: var(--cta-accent);" data-lang-id="Beli Kode Akses" data-lang-en="Buy Access Code"></button>
                    </div>
                </div>
            </main>

            <footer class="border-t-2 border-[var(--text-secondary)]/20 py-10">
                <div class="container mx-auto px-6 text-center font-label text-[var(--text-secondary)]">
                    <p class="text-[var(--primary-accent)] text-lg mb-4"><span class="text-[var(--secondary-accent)]">$</span> techno-rebel build complete</p>
                    <div class="flex justify-center space-x-6 mb-4">
                        <a href="#" class="hover:text-primary-accent transition-colors">Instagram</a>
                        <span class="text-gray-700">/</span>
                        <a href="#" class="hover:text-primary-accent transition-colors">YouTube</a>
                        <span class="text-gray-700">/</span>
                        <a href="#" class="hover:text-primary-accent transition-colors">Threads</a>
                    </div>
                </div>
            </footer>
        </div>

        <!-- ==================== HALAMAN MEMBER AREA (DASHBOARD) ======================== -->
        <div id="member-area-page" class="page min-h-screen flex-col">
            <header class="sticky top-0 z-50 glass-card">
                <nav class="container mx-auto px-6 py-4 flex justify-between items-center">
                    <a href="#" onclick="showPage('member-area-page')" class="font-header text-2xl glitch-text" data-text="TECHNO-REBEL">TECHNO-REBEL</a>
                    <div class="hidden md:flex items-center space-x-8 font-label">
                        <a href="#" onclick="showPage('kits-list-page')" class="text-[var(--text-secondary)] hover:text-[var(--primary-accent)] transition-colors" data-lang-id="Etalase Proyek" data-lang-en="Project Catalog"></a>
                        <a href="#" onclick="showPage('tutorials-list-page')" class="text-[var(--text-secondary)] hover:text-[var(--primary-accent)] transition-colors" data-lang-id="Tutorial Hype" data-lang-en="Hype Tutorials"></a>
                    </div>
                    <div class="flex items-center space-x-6 font-label">
                        <span class="text-[var(--text-secondary)] hidden md:inline">Welcome, <span class="text-[var(--text-primary)]">rebel_01</span></span>
                        <a href="#" onclick="showPage('account-settings-page')" class="text-[var(--text-primary)] hover:text-primary-accent transition-colors"><i data-feather="settings"></i></a>
                        <a href="#" onclick="showPage('landing-page')" class="px-4 py-2 rounded-lg text-sm font-label border-2 border-[var(--primary-accent)] text-[var(--primary-accent)] hover:bg-[var(--primary-accent)] hover:text-black transition-colors">LOGOUT</a>
                    </div>
                </nav>
            </header>
            <main class="container mx-auto px-6 py-12 flex-grow">
                <h1 class="font-header text-4xl mb-8" data-lang-id="Markas Besar" data-lang-en="Headquarters"></h1>

                <!-- Dashboard Stats -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div class="glass-card rounded-lg p-4 text-center">
                        <i data-feather="package" class="w-8 h-8 mx-auto mb-2 text-[var(--primary-accent)]"></i>
                        <div class="font-header text-2xl text-[var(--primary-accent)]">2</div>
                        <div class="font-label text-sm text-[var(--text-secondary)]" data-lang-id="Kit Dimiliki" data-lang-en="Owned Kits">Kit Dimiliki</div>
                    </div>
                    <div class="glass-card rounded-lg p-4 text-center">
                        <i data-feather="play-circle" class="w-8 h-8 mx-auto mb-2 text-[var(--cta-accent)]"></i>
                        <div class="font-header text-2xl text-[var(--cta-accent)]">8</div>
                        <div class="font-label text-sm text-[var(--text-secondary)]" data-lang-id="Tutorial Premium" data-lang-en="Premium Tutorials">Tutorial Premium</div>
                    </div>
                    <div class="glass-card rounded-lg p-4 text-center">
                        <i data-feather="check-circle" class="w-8 h-8 mx-auto mb-2 text-[var(--secondary-accent)]"></i>
                        <div class="font-header text-2xl text-[var(--secondary-accent)]">5</div>
                        <div class="font-label text-sm text-[var(--text-secondary)]" data-lang-id="Tutorial Selesai" data-lang-en="Completed Tutorials">Tutorial Selesai</div>
                    </div>
                    <div class="glass-card rounded-lg p-4 text-center">
                        <i data-feather="trending-up" class="w-8 h-8 mx-auto mb-2 text-[var(--primary-accent)]"></i>
                        <div class="font-header text-2xl text-[var(--primary-accent)]">75%</div>
                        <div class="font-label text-sm text-[var(--text-secondary)]" data-lang-id="Progress Total" data-lang-en="Total Progress">Progress Total</div>
                    </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <div class="lg:col-span-2 space-y-8">
                        <!-- Continue Learning Section -->
                        <section>
                            <h2 class="font-tech-mono text-2xl text-[var(--primary-accent)] mb-4" data-lang-id="// Lanjutin Oprekan Lo" data-lang-en="// Continue Your Project"></h2>
                            <div class="glass-card rounded-xl p-6 group border border-gray-700 hover:border-secondary-accent transition-all cursor-pointer" onclick="showPage('tutorial-player-page')">
                                <div class="flex items-start gap-4 mb-4">
                                    <img src="https://placehold.co/80x60/120D21/EAEAEA?text=OLED" alt="Tutorial Thumbnail" class="rounded border border-[var(--primary-accent)]">
                                    <div class="flex-1">
                                        <h3 class="font-tech-mono text-xl mb-2">Video 2: Tampilkan ke OLED</h3>
                                        <p class="text-[var(--text-secondary)] text-sm mb-2">ESP32 Trainer Kit Course</p>
                                        <div class="flex items-center gap-2 text-xs">
                                            <span class="bg-[var(--cta-accent)] text-black px-2 py-1 rounded-full">Premium</span>
                                            <span class="text-[var(--text-secondary)]">22 menit</span>
                                        </div>
                                    </div>
                                </div>
                                <p class="text-[var(--text-secondary)] text-sm mb-4">Lanjutkan dari mana Anda tinggalkan. Selesaikan modul untuk membuka potensi penuh kit Anda.</p>
                                <div class="progress-bar-container rounded-full h-4 overflow-hidden">
                                    <div class="progress-bar h-full rounded-full" style="width: 65%;"></div>
                                </div>
                                <div class="text-right font-label text-xs text-[var(--primary-accent)] mt-1">65% Complete</div>
                            </div>
                        </section>

                        <!-- My Kits Section -->
                        <section>
                            <h2 class="font-tech-mono text-2xl text-[var(--primary-accent)] mb-4" data-lang-id="// Amunisi Lo" data-lang-en="// Your Arsenal"></h2>
                            <div class="space-y-4">
                                <!-- Kit 1 -->
                                <div class="glass-card rounded-xl p-6 border border-gray-700">
                                    <div class="flex items-start gap-4 mb-4">
                                        <img src="https://placehold.co/80x80/120D21/00F5D4?text=ESP32" alt="ESP32 Kit" class="rounded-md border border-[var(--primary-accent)]">
                                        <div class="flex-1">
                                            <h4 class="font-tech-mono text-lg">ESP32 Trainer Kit</h4>
                                            <p class="text-sm text-[var(--text-secondary)] mb-2">Purchased: 2025-07-31</p>
                                            <div class="flex items-center gap-2 mb-3">
                                                <span class="bg-[var(--primary-accent)] text-black px-2 py-1 rounded-full text-xs">Active</span>
                                                <span class="text-[var(--cta-accent)] text-xs">+5 Premium Tutorials</span>
                                            </div>
                                        </div>
                                        <div class="text-right">
                                            <div class="font-header text-lg text-[var(--primary-accent)]">75%</div>
                                            <div class="text-xs text-[var(--text-secondary)]">Progress</div>
                                        </div>
                                    </div>

                                    <!-- Premium Tutorials dari Kit -->
                                    <div class="bg-gray-800/50 rounded-lg p-4 mb-4">
                                        <h5 class="font-label text-sm text-[var(--cta-accent)] mb-2">🎁 Tutorial Premium Gratis:</h5>
                                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-2 text-xs">
                                            <div class="flex items-center gap-2">
                                                <i data-feather="check-circle" class="w-3 h-3 text-[var(--primary-accent)]"></i>
                                                <span>Video 1: Baca Suhu DHT11</span>
                                            </div>
                                            <div class="flex items-center gap-2">
                                                <i data-feather="play-circle" class="w-3 h-3 text-[var(--cta-accent)]"></i>
                                                <span>Video 2: Tampilkan ke OLED</span>
                                            </div>
                                            <div class="flex items-center gap-2">
                                                <i data-feather="lock" class="w-3 h-3 text-[var(--text-secondary)]"></i>
                                                <span>Video 3: Kontrol via WiFi</span>
                                            </div>
                                            <div class="flex items-center gap-2">
                                                <i data-feather="lock" class="w-3 h-3 text-[var(--text-secondary)]"></i>
                                                <span>Video 4: Kirim Data ke Server</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="flex gap-2">
                                        <button onclick="showPage('kit-detail-page', { from: 'member-area-page', kitId: 'esp32-trainer' })" class="flex-1 btn-pro text-sm py-2">Lihat Detail</button>
                                        <button onclick="showPage('tutorials-list-page')" class="px-4 py-2 rounded-lg text-sm border border-[var(--cta-accent)] text-[var(--cta-accent)] hover:bg-[var(--cta-accent)] hover:text-black transition-colors">Tutorial</button>
                                    </div>
                                </div>

                                <!-- Kit 2 -->
                                <div class="glass-card rounded-xl p-6 border border-gray-700">
                                    <div class="flex items-start gap-4 mb-4">
                                        <img src="https://placehold.co/80x80/120D21/9B5DE5?text=IOT" alt="IoT Security Kit" class="rounded-md border border-[var(--secondary-accent)]">
                                        <div class="flex-1">
                                            <h4 class="font-tech-mono text-lg">IoT Security Kit</h4>
                                            <p class="text-sm text-[var(--text-secondary)] mb-2">Purchased: 2025-07-25</p>
                                            <div class="flex items-center gap-2 mb-3">
                                                <span class="bg-[var(--secondary-accent)] text-white px-2 py-1 rounded-full text-xs">Active</span>
                                                <span class="text-[var(--cta-accent)] text-xs">+3 Premium Tutorials</span>
                                            </div>
                                        </div>
                                        <div class="text-right">
                                            <div class="font-header text-lg text-[var(--secondary-accent)]">30%</div>
                                            <div class="text-xs text-[var(--text-secondary)]">Progress</div>
                                        </div>
                                    </div>

                                    <!-- Premium Tutorials dari Kit -->
                                    <div class="bg-gray-800/50 rounded-lg p-4 mb-4">
                                        <h5 class="font-label text-sm text-[var(--cta-accent)] mb-2">🎁 Tutorial Premium Gratis:</h5>
                                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-2 text-xs">
                                            <div class="flex items-center gap-2">
                                                <i data-feather="check-circle" class="w-3 h-3 text-[var(--primary-accent)]"></i>
                                                <span>Security Basics</span>
                                            </div>
                                            <div class="flex items-center gap-2">
                                                <i data-feather="lock" class="w-3 h-3 text-[var(--text-secondary)]"></i>
                                                <span>Camera Integration</span>
                                            </div>
                                            <div class="flex items-center gap-2">
                                                <i data-feather="lock" class="w-3 h-3 text-[var(--text-secondary)]"></i>
                                                <span>Motion Detection</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="flex gap-2">
                                        <button onclick="showPage('kit-detail-page', { from: 'member-area-page', kitId: 'iot-security' })" class="flex-1 btn-pro text-sm py-2">Lihat Detail</button>
                                        <button onclick="showPage('tutorials-list-page')" class="px-4 py-2 rounded-lg text-sm border border-[var(--cta-accent)] text-[var(--cta-accent)] hover:bg-[var(--cta-accent)] hover:text-black transition-colors">Tutorial</button>
                                    </div>
                                </div>
                            </div>
                        </section>
                    </div>

                    <div class="space-y-8">
                        <!-- Tutorial Progress -->
                        <section>
                            <h2 class="font-tech-mono text-2xl text-[var(--primary-accent)] mb-4" data-lang-id="// Gudang Ilmu Lo" data-lang-en="// Your Knowledge Base"></h2>
                            <div class="space-y-3">
                                <div class="glass-card rounded-lg p-4 flex items-center justify-between">
                                    <div class="flex items-center gap-3">
                                        <i data-feather="check-circle" class="text-[var(--primary-accent)]"></i>
                                        <div>
                                            <h4 class="font-label text-sm">Video 1: Baca Suhu DHT11</h4>
                                            <p class="text-xs text-[var(--text-secondary)]">ESP32 Trainer Kit</p>
                                        </div>
                                    </div>
                                    <span class="font-label text-xs py-1 px-2 rounded-full bg-[var(--primary-accent)] text-black">Tamat</span>
                                </div>

                                <div class="glass-card rounded-lg p-4 flex items-center justify-between cursor-pointer hover:bg-gray-800" onclick="showPage('tutorial-player-page')">
                                    <div class="flex items-center gap-3">
                                        <i data-feather="play-circle" class="text-[var(--cta-accent)]"></i>
                                        <div>
                                            <h4 class="font-label text-sm">Video 2: Tampilkan ke OLED</h4>
                                            <p class="text-xs text-[var(--text-secondary)]">ESP32 Trainer Kit</p>
                                        </div>
                                    </div>
                                    <span class="font-label text-xs py-1 px-2 rounded-full bg-[var(--cta-accent)] text-black">Premium</span>
                                </div>

                                <div class="glass-card rounded-lg p-4 flex items-center justify-between opacity-50">
                                    <div class="flex items-center gap-3">
                                        <i data-feather="lock" class="text-gray-500"></i>
                                        <div>
                                            <h4 class="font-label text-sm text-gray-500">Video 3: Kontrol via WiFi</h4>
                                            <p class="text-xs text-gray-500">ESP32 Trainer Kit</p>
                                        </div>
                                    </div>
                                    <span class="font-label text-xs py-1 px-2 rounded-full bg-gray-700 text-gray-400">Terkunci</span>
                                </div>

                                <div class="glass-card rounded-lg p-4 flex items-center justify-between">
                                    <div class="flex items-center gap-3">
                                        <i data-feather="check-circle" class="text-[var(--primary-accent)]"></i>
                                        <div>
                                            <h4 class="font-label text-sm">Security Basics</h4>
                                            <p class="text-xs text-[var(--text-secondary)]">IoT Security Kit</p>
                                        </div>
                                    </div>
                                    <span class="font-label text-xs py-1 px-2 rounded-full bg-[var(--primary-accent)] text-black">Tamat</span>
                                </div>
                            </div>
                        </section>

                        <!-- Account Section -->
                        <section>
                            <h2 class="font-tech-mono text-2xl text-[var(--primary-accent)] mb-4" data-lang-id="// Akun" data-lang-en="// Account"></h2>
                            <div class="glass-card rounded-xl p-6 border border-gray-700">
                                <ul class="space-y-3 font-label">
                                    <li><a href="#" onclick="showPage('account-settings-page')" class="flex items-center gap-3 hover:text-primary-accent transition-colors"><i data-feather="user"></i> <span data-lang-id="Pengaturan Akun" data-lang-en="Account Settings"></span></a></li>
                                    <li><a href="#" onclick="openModal('activation-modal')" class="flex items-center gap-3 hover:text-primary-accent transition-colors"><i data-feather="key"></i> <span data-lang-id="Punya Kode Redeem?" data-lang-en="Got a Redeem Code?"></span></a></li>
                                    <li><a href="#" onclick="showPage('admin-page')" class="flex items-center gap-3 hover:text-primary-accent transition-colors"><i data-feather="sliders"></i> <span data-lang-id="Panel Admin" data-lang-en="Admin Panel"></span></a></li>
                                    <li><a href="#" onclick="showPage('landing-page')" class="flex items-center gap-3 hover:text-primary-accent transition-colors"><i data-feather="log-out"></i> <span data-lang-id="Keluar" data-lang-en="Logout"></span></a></li>
                                </ul>
                            </div>
                        </section>
                    </div>
                </div>
            </main><footer class="border-t-2 border-[var(--text-secondary)]/20 py-6"><div class="container mx-auto px-6 text-center font-label text-[var(--text-secondary)] text-xs"><p>$ session.active | user: rebel_01 | status: online</p></div></footer>
        </div>

        <!-- ==================== HALAMAN DETAIL KIT ======================== -->
        <div id="kit-detail-page" class="page min-h-screen flex-col">
            <header class="sticky top-0 z-50 glass-card">
                <nav class="container mx-auto px-6 py-4 flex justify-between items-center">
                    <a id="kit-detail-back-button" href="#" class="font-header text-2xl text-[var(--primary-accent)] flex items-center gap-3">
                        <i data-feather="arrow-left"></i>
                        <span id="kit-detail-back-text"></span>
                    </a>
                    <div id="kit-detail-action-button-container" class="flex items-center space-x-6 font-label"></div>
                </nav>
            </header>
            <main class="container mx-auto px-6 py-12 flex-grow">
                <div class="grid grid-cols-1 lg:grid-cols-5 gap-8 lg:gap-12">
                    <!-- Kit Images -->
                    <div class="lg:col-span-3">
                        <div class="glass-card rounded-lg p-4 border border-gray-700">
                            <img id="main-kit-image" src="https://placehold.co/800x600/120D21/EAEAEA?text=Robot+Nolep+Kit" alt="[Gambar utama dari Robot Nolep Kit]" class="w-full rounded-md mb-4">
                            <div class="grid grid-cols-4 gap-4">
                                <img src="https://placehold.co/200x150/120D21/00F5D4?text=View+1" alt="[Tampilan kit dari samping]" class="cursor-pointer rounded border-2 border-[var(--primary-accent)]" onclick="document.getElementById('main-kit-image').src=this.src">
                                <img src="https://placehold.co/200x150/120D21/EAEAEA?text=View+2" alt="[Tampilan kit dari atas]" class="cursor-pointer rounded border-2 border-transparent hover:border-primary-accent" onclick="document.getElementById('main-kit-image').src=this.src">
                                <img src="https://placehold.co/200x150/120D21/EAEAEA?text=PCB+Detail" alt="[Detail PCB dari kit]" class="cursor-pointer rounded border-2 border-transparent hover:border-primary-accent" onclick="document.getElementById('main-kit-image').src=this.src">
                                <img src="https://placehold.co/200x150/120D21/EAEAEA?text=Components" alt="[Semua komponen kit]" class="cursor-pointer rounded border-2 border-transparent hover:border-primary-accent" onclick="document.getElementById('main-kit-image').src=this.src">
                            </div>
                        </div>

                        <!-- Premium Tutorials Included -->
                        <div class="glass-card rounded-lg p-6 mt-6 border border-[var(--cta-accent)]">
                            <h3 class="font-tech-mono text-xl text-[var(--cta-accent)] mb-4 flex items-center gap-2">
                                <i data-feather="gift"></i>
                                // Tutorial Premium Gratis
                            </h3>
                            <p class="text-[var(--text-secondary)] text-sm mb-4">Beli kit ini dan dapatkan akses gratis ke tutorial premium berikut:</p>

                            <div class="space-y-3">
                                <div class="flex items-center gap-3 p-3 bg-gray-800/50 rounded-lg">
                                    <i data-feather="play-circle" class="text-[var(--cta-accent)] w-5 h-5"></i>
                                    <div class="flex-1">
                                        <h4 class="font-label text-sm">Video 1: Baca Suhu DHT11</h4>
                                        <p class="text-xs text-[var(--text-secondary)]">15 menit • Pemula</p>
                                    </div>
                                    <span class="text-xs bg-[var(--cta-accent)] text-black px-2 py-1 rounded-full">Premium</span>
                                </div>

                                <div class="flex items-center gap-3 p-3 bg-gray-800/50 rounded-lg">
                                    <i data-feather="play-circle" class="text-[var(--cta-accent)] w-5 h-5"></i>
                                    <div class="flex-1">
                                        <h4 class="font-label text-sm">Video 2: Tampilkan ke OLED</h4>
                                        <p class="text-xs text-[var(--text-secondary)]">22 menit • Menengah</p>
                                    </div>
                                    <span class="text-xs bg-[var(--cta-accent)] text-black px-2 py-1 rounded-full">Premium</span>
                                </div>

                                <div class="flex items-center gap-3 p-3 bg-gray-800/50 rounded-lg">
                                    <i data-feather="play-circle" class="text-[var(--cta-accent)] w-5 h-5"></i>
                                    <div class="flex-1">
                                        <h4 class="font-label text-sm">Video 3: Kontrol via WiFi</h4>
                                        <p class="text-xs text-[var(--text-secondary)]">28 menit • Menengah</p>
                                    </div>
                                    <span class="text-xs bg-[var(--cta-accent)] text-black px-2 py-1 rounded-full">Premium</span>
                                </div>
                            </div>

                            <div class="mt-4 p-3 bg-[var(--cta-accent)]/10 rounded-lg border border-[var(--cta-accent)]/30">
                                <p class="text-xs text-[var(--cta-accent)]">💡 <strong>Total Nilai:</strong> Tutorial premium senilai Rp 150.000 GRATIS!</p>
                            </div>
                        </div>
                    </div>

                    <!-- Kit Info -->
                    <div class="lg:col-span-2">
                        <h1 class="font-header text-4xl mb-2">Proyek: Robot Nolep</h1>
                        <div class="flex items-center gap-4 mb-4">
                            <p class="font-label text-2xl text-[var(--primary-accent)]">Rp 350.000</p>
                            <span class="bg-[var(--primary-accent)] text-black px-3 py-1 rounded-full text-sm font-label">Pemula</span>
                        </div>

                        <p class="text-[var(--text-secondary)] mb-6">Rakit robot yang jago ghosting tembok dan rintangan lain. *No cap*, dia lebih jago jaga jarak daripada lo. Cocok buat pemula yang mau langsung liat hasil nyata.</p>

                        <!-- Kit Contents -->
                        <div class="glass-card rounded-lg p-6 mb-6 border border-gray-700">
                            <h3 class="font-tech-mono text-xl text-[var(--primary-accent)] mb-4">// Isi Kotak Ajaib</h3>
                            <ul class="space-y-2 font-label text-sm">
                                <li class="flex items-center gap-3"><i data-feather="check" class="text-[var(--primary-accent)] w-4 h-4"></i><span>1x ESP32 Trainer Board</span></li>
                                <li class="flex items-center gap-3"><i data-feather="check" class="text-[var(--primary-accent)] w-4 h-4"></i><span>1x OLED Display SSD1306</span></li>
                                <li class="flex items-center gap-3"><i data-feather="check" class="text-[var(--primary-accent)] w-4 h-4"></i><span>1x Sensor Suhu & Kelembaban DHT11</span></li>
                                <li class="flex items-center gap-3"><i data-feather="check" class="text-[var(--primary-accent)] w-4 h-4"></i><span>3x Push Button</span></li>
                                <li class="flex items-center gap-3"><i data-feather="check" class="text-[var(--primary-accent)] w-4 h-4"></i><span>1x Potentiometer</span></li>
                                <li class="flex items-center gap-3"><i data-feather="check" class="text-[var(--primary-accent)] w-4 h-4"></i><span>1x Kabel USB-C</span></li>
                                <li class="flex items-center gap-3"><i data-feather="check" class="text-[var(--primary-accent)] w-4 h-4"></i><span>1x Panduan Lengkap</span></li>
                            </ul>
                        </div>

                        <!-- What You'll Learn -->
                        <div class="glass-card rounded-lg p-6 mb-6 border border-gray-700">
                            <h3 class="font-tech-mono text-xl text-[var(--secondary-accent)] mb-4">// Yang Bakal Lo Pelajari</h3>
                            <ul class="space-y-2 font-label text-sm">
                                <li class="flex items-center gap-3"><i data-feather="zap" class="text-[var(--secondary-accent)] w-4 h-4"></i><span>Dasar-dasar ESP32 dan IoT</span></li>
                                <li class="flex items-center gap-3"><i data-feather="zap" class="text-[var(--secondary-accent)] w-4 h-4"></i><span>Sensor suhu dan kelembaban</span></li>
                                <li class="flex items-center gap-3"><i data-feather="zap" class="text-[var(--secondary-accent)] w-4 h-4"></i><span>Display OLED programming</span></li>
                                <li class="flex items-center gap-3"><i data-feather="zap" class="text-[var(--secondary-accent)] w-4 h-4"></i><span>WiFi connectivity</span></li>
                                <li class="flex items-center gap-3"><i data-feather="zap" class="text-[var(--secondary-accent)] w-4 h-4"></i><span>Web server basics</span></li>
                            </ul>
                        </div>

                        <!-- Purchase Buttons -->
                        <div class="space-y-3">
                            <button onclick="buyKit('robot-nolep')" class="w-full btn-pro text-lg py-3">
                                <i data-feather="shopping-cart" class="w-5 h-5 mr-2"></i>
                                Beli di Tokopedia
                            </button>
                            <button onclick="buyPremiumCode()" class="w-full px-4 py-3 rounded-lg text-sm border-2 border-[var(--cta-accent)] text-[var(--cta-accent)] hover:bg-[var(--cta-accent)] hover:text-black transition-colors">
                                <i data-feather="message-circle" class="w-4 h-4 mr-2"></i>
                                Tanya via WhatsApp
                            </button>
                        </div>

                        <!-- Kit Stats -->
                        <div class="grid grid-cols-3 gap-4 mt-6">
                            <div class="text-center">
                                <div class="font-header text-lg text-[var(--primary-accent)]">4.8</div>
                                <div class="text-xs text-[var(--text-secondary)]">Rating</div>
                            </div>
                            <div class="text-center">
                                <div class="font-header text-lg text-[var(--primary-accent)]">150+</div>
                                <div class="text-xs text-[var(--text-secondary)]">Terjual</div>
                            </div>
                            <div class="text-center">
                                <div class="font-header text-lg text-[var(--primary-accent)]">3</div>
                                <div class="text-xs text-[var(--text-secondary)]">Tutorial</div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>

        <!-- ==================== HALAMAN PRATINJAU TUTORIAL (REVISED) ======================== -->
        <div id="tutorial-preview-page" class="page min-h-screen flex-col">
            <header class="sticky top-0 z-50 glass-card">
                <nav class="container mx-auto px-6 py-4 flex justify-between items-center">
                    <a href="#" onclick="showPage('tutorials-list-page')" class="font-header text-2xl text-[var(--primary-accent)] flex items-center gap-3">
                        <i data-feather="arrow-left"></i>
                        <span data-lang-id="Kembali ke Tutorial" data-lang-en="Back to Tutorials">Kembali ke Tutorial</span>
                    </a>
                    <div class="flex items-center space-x-6 font-label">
                        <a href="#" onclick="showPage('login-page')" class="px-4 py-2 rounded-lg text-sm font-label border-2 border-[var(--primary-accent)] text-[var(--primary-accent)] hover:bg-[var(--primary-accent)] hover:text-black transition-colors">LOGIN</a>
                    </div>
                </nav>
            </header>
            <main class="container mx-auto px-6 py-12 flex-grow">
                <!-- Video Player dengan Overlay -->
                <div class="relative aspect-w-16 aspect-h-9 mb-4">
                    <img src="https://placehold.co/1280x720/120D21/EAEAEA?text=Video+Pratinjau" alt="[Pratinjau Video Tutorial]" class="w-full h-full object-cover rounded-lg border-2 border-gray-700">
                    <div class="absolute inset-0 flex flex-col items-center justify-center text-center bg-black bg-opacity-60 m-0 rounded-lg">
                        <i data-feather="play-circle" class="w-20 h-20 text-[var(--primary-accent)] mb-4"></i>
                        <h2 class="font-header text-3xl mb-2">Video Terkunci</h2>
                        <p class="text-[var(--text-secondary)] max-w-md mb-8">Login atau buat akun untuk menonton video ini selengkapnya.</p>
                    </div>
                </div>

                <!-- Konten Pratinjau -->
                <h1 id="preview-title" class="font-header text-3xl mt-6 mb-2"></h1>
                <p class="font-label text-[var(--text-secondary)] mb-6">ESP32 Trainer Kit Course</p>
                
                <div class="glass-card rounded-lg p-6">
                    <h3 class="font-tech-mono text-xl text-[var(--primary-accent)] mb-4">// Deskripsi Pelajaran</h3>
                    <p class="text-[var(--text-secondary)]">
                        Di video ini, kita akan belajar cara mengirim data sensor yang sudah kita baca di pelajaran sebelumnya untuk ditampilkan di layar OLED SSD1306. Ini adalah langkah penting untuk membuat proyek IoT yang interaktif dan memberikan feedback visual kepada pengguna. Kita akan membahas library yang dibutuhkan, cara inisialisasi layar, dan bagaimana menampilkan teks serta variabel.
                    </p>
                </div>
                
                <!-- Konten Terkunci -->
                <div class="relative mt-8">
                    <!-- Overlay Gradien dan CTA -->
                    <div class="absolute inset-0 flex flex-col justify-end items-center text-center z-20 p-8">
                        <div class="glass-card p-8 rounded-lg border border-[var(--secondary-accent)]">
                            <i data-feather="lock" class="w-12 h-12 text-[var(--secondary-accent)] mx-auto mb-4"></i>
                            <h3 id="locked-cta-heading" class="font-header text-2xl mb-2">Buka Konten Lengkap</h3>
                            <p id="locked-cta-text" class="text-[var(--text-secondary)] max-w-md mb-6">Dapatkan akses ke sisa artikel, kode sumber, dan forum diskusi.</p>
                            <div id="locked-cta-buttons" class="flex flex-col sm:flex-row gap-4">
                                <!-- Tombol dinamis akan dimasukkan di sini oleh JS -->
                            </div>
                        </div>
                    </div>

                    <!-- Konten yang dikaburkan -->
                    <div class="blur-md brightness-50 select-none">
                        <div class="glass-card rounded-lg p-6 mt-4">
                            <p class="text-[var(--text-secondary)]">
                                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
                            </p>
                            <h3 class="font-tech-mono text-xl text-[var(--primary-accent)] mt-6 mb-4">// Kode Sumber</h3>
                            <pre class="rounded-lg"><code class="language-cpp">// Kode sumber terkunci
#include &lt;Wire.h&gt;
#include &lt;Adafruit_GFX.h&gt;
#include &lt;Adafruit_SSD1306.h&gt;

// ... sisa kode tidak ditampilkan ...
                            </code></pre>
                        </div>
                    </div>
                </div>
            </main>
        </div>

        <!-- ==================== HALAMAN PEMUTAR VIDEO TUTORIAL (MEMBER) ======================== -->
        <div id="tutorial-player-page" class="page min-h-screen flex-col">
            <header class="sticky top-0 z-50 glass-card">
                <nav class="container mx-auto px-6 py-4 flex justify-between items-center">
                    <a href="#" onclick="showPage('member-area-page')" class="font-header text-2xl text-[var(--primary-accent)] flex items-center gap-3">
                        <i data-feather="arrow-left"></i>
                        <span data-lang-id="Back to Dashboard" data-lang-en="Back to Dashboard">Back to Dashboard</span>
                    </a>
                    <div class="flex items-center space-x-6 font-label">
                        <span class="text-[var(--text-secondary)] hidden md:inline">Welcome, <span class="text-[var(--text-primary)]">rebel_01</span></span>
                        <a href="#" onclick="showPage('landing-page')" class="px-4 py-2 rounded-lg text-sm font-label border-2 border-[var(--primary-accent)] text-[var(--primary-accent)] hover:bg-[var(--primary-accent)] hover:text-black transition-colors">LOGOUT</a>
                    </div>
                </nav>
            </header>
            <main class="container mx-auto px-6 py-12 flex-grow"><div class="grid grid-cols-1 lg:grid-cols-3 gap-8"><div class="lg:col-span-2"><div class="aspect-w-16 aspect-h-9 mb-4"><iframe src="https://www.youtube.com/embed/dQw4w9WgXcQ" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen class="w-full h-full rounded-lg border-2 border-gray-700"></iframe></div><h1 class="font-header text-3xl mb-2">Video 2: Tampilkan ke OLED</h1><p class="font-label text-[var(--text-secondary)] mb-6">ESP32 Trainer Kit Course</p><div class="border-b border-gray-700"><nav class="-mb-px flex space-x-8" aria-label="Tabs"><a href="#" id="tab-deskripsi" onclick="showTutorialTab('deskripsi-content')" class="tutorial-tab font-label border-primary-accent text-primary-accent whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">Deskripsi</a><a href="#" id="tab-kode" onclick="showTutorialTab('kode-content')" class="tutorial-tab font-label border-transparent text-gray-400 hover:text-gray-200 hover:border-gray-500 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">Kode Sumber</a><a href="#" id="tab-diskusi" onclick="showTutorialTab('diskusi-content')" class="tutorial-tab font-label border-transparent text-gray-400 hover:text-gray-200 hover:border-gray-500 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">Diskusi</a></nav></div><div id="tutorial-content-container" class="py-6"><div id="deskripsi-content" class="tutorial-tab-content active glass-card p-6 mt-4 rounded-lg"><h3 class="font-tech-mono text-xl mb-2">Tentang Video Ini</h3><p class="text-[var(--text-secondary)]">Di video ini, kita akan belajar cara mengirim data sensor yang sudah kita baca di pelajaran sebelumnya untuk ditampilkan di layar OLED SSD1306. Ini adalah langkah penting untuk membuat proyek IoT yang interaktif.</p></div><div id="kode-content" class="tutorial-tab-content glass-card p-6 mt-4 rounded-lg"><h3 class="font-tech-mono text-xl mb-2">Kode Sumber</h3><pre class="mt-4 rounded-lg"><code class="language-cpp">// Contoh kode
#include &lt;Wire.h&gt;
#include &lt;Adafruit_GFX.h&gt;
#include &lt;Adafruit_SSD1306.h&gt;
#define SCREEN_WIDTH 128
#define SCREEN_HEIGHT 64
Adafruit_SSD1306 display(SCREEN_WIDTH, SCREEN_HEIGHT, &Wire, -1);
void setup() { Serial.begin(115200); if(!display.begin(SSD1306_SWITCHCAPVCC, 0x3C)) { Serial.println(F("SSD1306 allocation failed")); for(;;); } display.display(); delay(2000); display.clearDisplay(); }
void loop() { display.setTextSize(1); display.setTextColor(WHITE); display.setCursor(0, 10); display.println("Hello, Rebel!"); display.display(); }
</code></pre></div><div id="diskusi-content" class="tutorial-tab-content glass-card p-6 mt-4 rounded-lg"><h3 class="font-tech-mono text-xl mb-2">Diskusi</h3><p class="text-[var(--text-secondary)]">Fitur diskusi akan segera hadir. Bertanyalah dan bagikan proyek Anda dengan komunitas!</p></div></div></div><div class="lg:col-span-1"><div class="glass-card rounded-xl p-4 border border-gray-700 h-full"><h2 class="font-tech-mono text-xl text-[var(--primary-accent)] mb-4 p-2">// Course Content</h2><ul class="space-y-2 font-label custom-scrollbar overflow-y-auto" style="max-height: 70vh;"><li class="p-3 rounded-lg flex items-center gap-3 bg-gray-800"><i data-feather="check-circle" class="text-[var(--primary-accent)]"></i><span>1. Baca Suhu DHT11</span></li><li class="p-3 rounded-lg flex items-center gap-3 bg-primary-accent bg-opacity-10 border border-primary-accent"><i data-feather="play-circle" class="text-[var(--primary-accent)]"></i><span>2. Tampilkan ke OLED</span></li><li class="p-3 rounded-lg flex items-center gap-3 hover:bg-gray-800 opacity-60"><i data-feather="lock" class="text-gray-400"></i><span>3. Kontrol via WiFi</span></li><li class="p-3 rounded-lg flex items-center gap-3 hover:bg-gray-800 opacity-60"><i data-feather="lock" class="text-gray-400"></i><span>4. Kirim Data ke Server</span></li></ul></div></div></div></main>
        </div>
        
        <!-- ==================== HALAMAN PENGATURAN AKUN ======================== -->
        <div id="account-settings-page" class="page min-h-screen flex-col">
            <header class="sticky top-0 z-50 glass-card">
                <nav class="container mx-auto px-6 py-4 flex justify-between items-center">
                    <a href="#" onclick="showPage('member-area-page')" class="font-header text-2xl text-[var(--primary-accent)] flex items-center gap-3">
                        <i data-feather="arrow-left"></i>
                        <span data-lang-id="Back to Dashboard" data-lang-en="Back to Dashboard">Back to Dashboard</span>
                    </a>
                    <div class="flex items-center space-x-6 font-label">
                        <a href="#" onclick="showPage('landing-page')" class="px-4 py-2 rounded-lg text-sm font-label border-2 border-[var(--primary-accent)] text-[var(--primary-accent)] hover:bg-[var(--primary-accent)] hover:text-black transition-colors">LOGOUT</a>
                    </div>
                </nav>
            </header>
            <main class="container mx-auto px-6 py-12 flex-grow"><h1 class="font-header text-4xl mb-8">Account Settings</h1><div class="glass-card rounded-xl p-8 border border-gray-700"><h2 class="font-tech-mono text-2xl text-[var(--primary-accent)] mb-6">// Edit Profile</h2><form id="settings-form" class="space-y-8"><div class="grid grid-cols-1 md:grid-cols-2 gap-8"><div class="input-group"><input type="text" class="input-pro" value="rebel_01" placeholder=" "><label class="input-label">Username</label></div><div class="input-group"><input type="email" class="input-pro" value="<EMAIL>" placeholder=" "><label class="input-label">Email</label></div><div class="input-group"><input type="password" class="input-pro" placeholder=" "><label class="input-label">New Password (Optional)</label></div><div class="input-group"><input type="password" class="input-pro" placeholder=" "><label class="input-label">Confirm Password</label></div></div><div class="mt-8"><button type="submit" class="btn-pro">SAVE CHANGES</button></div></form></div></main>
        </div>
        
        <!-- ==================== HALAMAN ADMIN (BARU) ======================== -->
        <div id="admin-page" class="page min-h-screen flex-col">
            <header class="sticky top-0 z-50 glass-card">
                <nav class="container mx-auto px-6 py-4 flex justify-between items-center">
                    <a href="#" onclick="showPage('member-area-page')" class="font-header text-2xl text-[var(--primary-accent)] flex items-center gap-3">
                        <i data-feather="arrow-left"></i>
                        <span data-lang-id="Back to Dashboard" data-lang-en="Back to Dashboard">Back to Dashboard</span>
                    </a>
                    <div class="flex items-center space-x-6 font-label">
                        <a href="#" onclick="showPage('landing-page')" class="px-4 py-2 rounded-lg text-sm font-label border-2 border-[var(--primary-accent)] text-[var(--primary-accent)] hover:bg-[var(--primary-accent)] hover:text-black transition-colors">LOGOUT</a>
                    </div>
                </nav>
            </header>
            <main class="container mx-auto px-6 py-12 flex-grow">
                <h1 class="font-header text-4xl mb-8">Admin Dashboard</h1>
                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
                    <div class="glass-card p-6 rounded-lg border-l-4 border-[var(--primary-accent)]"><h3 class="font-label text-lg text-[var(--text-secondary)]">Total Pengguna</h3><p class="font-header text-4xl">1,257</p></div>
                    <div class="glass-card p-6 rounded-lg border-l-4 border-[var(--secondary-accent)]"><h3 class="font-label text-lg text-[var(--text-secondary)]">Total Tutorial</h3><p class="font-header text-4xl">48</p></div>
                    <div class="glass-card p-6 rounded-lg border-l-4 border-[var(--cta-accent)]"><h3 class="font-label text-lg text-[var(--text-secondary)]">Total Kit</h3><p class="font-header text-4xl">5</p></div>
                </div>

                <!-- Manajemen Tutorial -->
                <div class="glass-card rounded-lg p-6 mb-8">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="font-tech-mono text-2xl text-[var(--primary-accent)]">// Manajemen Tutorial</h2>
                        <button onclick="openTutorialModal()" class="btn-pro text-sm py-2 px-4" style="--primary-accent: var(--cta-accent);">Tambah Baru</button>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="w-full text-left font-label">
                            <thead><tr class="border-b border-[var(--text-secondary)]/20"><th class="p-3">Judul</th><th class="p-3">Tipe</th><th class="p-3">Terkait Kit</th><th class="p-3">Aksi</th></tr></thead>
                            <tbody>
                                <tr class="border-b border-[var(--text-secondary)]/10">
                                    <td class="p-3">Video 1: Baca Suhu DHT11</td>
                                    <td class="p-3"><span class="text-xs py-1 px-2 rounded-full bg-[var(--primary-accent)] text-black">Gratis</span></td>
                                    <td class="p-3">-</td>
                                    <td class="p-3 flex gap-4"><a href="#" onclick="openTutorialModal(true, this)" class="text-[var(--text-secondary)] hover:text-primary-accent"><i data-feather="edit-2"></i></a><a href="#" onclick="deleteItem(this)" class="text-[var(--text-secondary)] hover:text-secondary-accent"><i data-feather="trash-2"></i></a></td>
                                </tr>
                                <tr class="border-b border-[var(--text-secondary)]/10">
                                    <td class="p-3">Video 2: Tampilkan ke OLED</td>
                                    <td class="p-3"><span class="text-xs py-1 px-2 rounded-full bg-[var(--cta-accent)] text-black">Premium</span></td>
                                    <td class="p-3">ESP32 Trainer Kit</td>
                                    <td class="p-3 flex gap-4"><a href="#" onclick="openTutorialModal(true, this)" class="text-[var(--text-secondary)] hover:text-primary-accent"><i data-feather="edit-2"></i></a><a href="#" onclick="deleteItem(this)" class="text-[var(--text-secondary)] hover:text-secondary-accent"><i data-feather="trash-2"></i></a></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Manajemen Kit -->
                <div class="glass-card rounded-lg p-6 mb-8">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="font-tech-mono text-2xl text-[var(--primary-accent)]">// Manajemen Kit</h2>
                        <button onclick="openKitModal()" class="btn-pro text-sm py-2 px-4" style="--primary-accent: var(--cta-accent);">Tambah Kit Baru</button>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="w-full text-left font-label">
                            <thead><tr class="border-b border-[var(--text-secondary)]/20"><th class="p-3">Nama Kit</th><th class="p-3">Harga</th><th class="p-3">Akses Tutorial</th><th class="p-3">Aksi</th></tr></thead>
                            <tbody id="kit-table-body">
                                <tr class="border-b border-[var(--text-secondary)]/10">
                                    <td class="p-3">ESP32 Trainer Kit</td>
                                    <td class="p-3">Rp 350.000</td>
                                    <td class="p-3">Tampilkan ke OLED, Kontrol via WiFi</td>
                                    <td class="p-3 flex gap-4"><a href="#" onclick="openKitModal(true, this)" class="text-[var(--text-secondary)] hover:text-primary-accent"><i data-feather="edit-2"></i></a><a href="#" onclick="deleteItem(this)" class="text-[var(--text-secondary)] hover:text-secondary-accent"><i data-feather="trash-2"></i></a></td>
                                </tr>
                                <tr class="border-b border-[var(--text-secondary)]/10">
                                    <td class="p-3">IoT Kit</td>
                                    <td class="p-3">Rp 450.000</td>
                                    <td class="p-3">Kontrol via WiFi</td>
                                    <td class="p-3 flex gap-4"><a href="#" onclick="openKitModal(true, this)" class="text-[var(--text-secondary)] hover:text-primary-accent"><i data-feather="edit-2"></i></a><a href="#" onclick="deleteItem(this)" class="text-[var(--text-secondary)] hover:text-secondary-accent"><i data-feather="trash-2"></i></a></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Kit Code Management -->
                <div class="glass-card rounded-lg p-6 mb-8">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="font-tech-mono text-2xl text-[var(--primary-accent)]">// Manajemen Kode Kit</h2>
                        <button onclick="openModal('generate-code-modal')" class="btn-pro text-sm">
                            <i data-feather="plus" class="w-4 h-4 mr-2"></i>
                            Generate Kode
                        </button>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                        <div class="bg-gray-800/50 rounded-lg p-4 text-center">
                            <div class="font-header text-2xl text-[var(--primary-accent)]">45</div>
                            <div class="text-sm text-[var(--text-secondary)]">Total Kode</div>
                        </div>
                        <div class="bg-gray-800/50 rounded-lg p-4 text-center">
                            <div class="font-header text-2xl text-[var(--cta-accent)]">12</div>
                            <div class="text-sm text-[var(--text-secondary)]">Sudah Dipakai</div>
                        </div>
                        <div class="bg-gray-800/50 rounded-lg p-4 text-center">
                            <div class="font-header text-2xl text-[var(--secondary-accent)]">33</div>
                            <div class="text-sm text-[var(--text-secondary)]">Belum Dipakai</div>
                        </div>
                    </div>

                    <div class="overflow-x-auto">
                        <table class="w-full text-left font-label">
                            <thead>
                                <tr class="border-b border-[var(--text-secondary)]/20">
                                    <th class="p-3">Kode</th>
                                    <th class="p-3">Kit</th>
                                    <th class="p-3">Status</th>
                                    <th class="p-3">Dipakai Oleh</th>
                                    <th class="p-3">Tanggal</th>
                                    <th class="p-3">Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="border-b border-[var(--text-secondary)]/10">
                                    <td class="p-3 font-mono text-sm">ROBOT-NOLEP-ABC123</td>
                                    <td class="p-3">Robot Nolep Kit</td>
                                    <td class="p-3"><span class="text-xs py-1 px-2 rounded-full bg-[var(--cta-accent)] text-black">Dipakai</span></td>
                                    <td class="p-3">rebel_01</td>
                                    <td class="p-3">2025-07-31</td>
                                    <td class="p-3">
                                        <button onclick="copyCode('ROBOT-NOLEP-ABC123')" class="text-[var(--text-secondary)] hover:text-primary-accent mr-2" title="Copy Code">
                                            <i data-feather="copy" class="w-4 h-4"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr class="border-b border-[var(--text-secondary)]/10">
                                    <td class="p-3 font-mono text-sm">ROBOT-NOLEP-DEF456</td>
                                    <td class="p-3">Robot Nolep Kit</td>
                                    <td class="p-3"><span class="text-xs py-1 px-2 rounded-full bg-[var(--secondary-accent)] text-white">Tersedia</span></td>
                                    <td class="p-3">-</td>
                                    <td class="p-3">-</td>
                                    <td class="p-3">
                                        <button onclick="copyCode('ROBOT-NOLEP-DEF456')" class="text-[var(--text-secondary)] hover:text-primary-accent mr-2" title="Copy Code">
                                            <i data-feather="copy" class="w-4 h-4"></i>
                                        </button>
                                        <button onclick="deleteCode('ROBOT-NOLEP-DEF456')" class="text-[var(--text-secondary)] hover:text-secondary-accent" title="Delete Code">
                                            <i data-feather="trash-2" class="w-4 h-4"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr class="border-b border-[var(--text-secondary)]/10">
                                    <td class="p-3 font-mono text-sm">WEATHER-STN-GHI789</td>
                                    <td class="p-3">Weather Station Kit</td>
                                    <td class="p-3"><span class="text-xs py-1 px-2 rounded-full bg-[var(--secondary-accent)] text-white">Tersedia</span></td>
                                    <td class="p-3">-</td>
                                    <td class="p-3">-</td>
                                    <td class="p-3">
                                        <button onclick="copyCode('WEATHER-STN-GHI789')" class="text-[var(--text-secondary)] hover:text-primary-accent mr-2" title="Copy Code">
                                            <i data-feather="copy" class="w-4 h-4"></i>
                                        </button>
                                        <button onclick="deleteCode('WEATHER-STN-GHI789')" class="text-[var(--text-secondary)] hover:text-secondary-accent" title="Delete Code">
                                            <i data-feather="trash-2" class="w-4 h-4"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Manajemen Pengguna -->
                <div class="glass-card rounded-lg p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="font-tech-mono text-2xl text-[var(--primary-accent)]">// Manajemen Pengguna</h2>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="w-full text-left font-label">
                            <thead><tr class="border-b border-[var(--text-secondary)]/20"><th class="p-3">Username</th><th class="p-3">Email</th><th class="p-3">Role</th><th class="p-3">Aksi</th></tr></thead>
                            <tbody>
                                <tr class="border-b border-[var(--text-secondary)]/10">
                                    <td class="p-3">rebel_01</td>
                                    <td class="p-3"><EMAIL></td>
                                    <td class="p-3"><span class="text-xs py-1 px-2 rounded-full bg-[var(--cta-accent)] text-black">Admin</span></td>
                                    <td class="p-3 flex gap-4"><a href="#" onclick="openUserModal(true, this)" class="text-[var(--text-secondary)] hover:text-primary-accent"><i data-feather="edit-2"></i></a><a href="#" onclick="deleteItem(this)" class="text-[var(--text-secondary)] hover:text-secondary-accent"><i data-feather="trash-2"></i></a></td>
                                </tr>
                                <tr class="border-b border-[var(--text-secondary)]/10">
                                    <td class="p-3">jane_doe</td>
                                    <td class="p-3"><EMAIL></td>
                                    <td class="p-3"><span class="text-xs py-1 px-2 rounded-full bg-[var(--text-secondary)] text-white">Member</span></td>
                                    <td class="p-3 flex gap-4"><a href="#" onclick="openUserModal(true, this)" class="text-[var(--text-secondary)] hover:text-primary-accent"><i data-feather="edit-2"></i></a><a href="#" onclick="deleteItem(this)" class="text-[var(--text-secondary)] hover:text-secondary-accent"><i data-feather="trash-2"></i></a></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </main>
        </div>

    </div>

    <!-- Modals -->
    <!-- ==================== MODAL AKTIVASI KODE KIT (ENHANCED) ======================== -->
    <div id="activation-modal" class="modal-container fixed inset-0 z-50 items-center justify-center bg-black bg-opacity-70">
        <div class="glass-card rounded-xl p-8 border border-[var(--primary-accent)] w-full max-w-lg relative">
            <button onclick="closeModal('activation-modal')" class="absolute top-4 right-4 text-[var(--text-secondary)] hover:text-white">
                <i data-feather="x" class="w-6 h-6"></i>
            </button>

            <div class="text-center mb-6">
                <div class="w-16 h-16 mx-auto mb-4 bg-[var(--primary-accent)]/20 rounded-full flex items-center justify-center">
                    <i data-feather="gift" class="w-8 h-8 text-[var(--primary-accent)]"></i>
                </div>
                <h2 class="font-header text-2xl text-[var(--primary-accent)] mb-2">Aktivasi Kode Kit</h2>
                <p class="text-[var(--text-secondary)] text-sm">Masukkan kode yang ada di dalam box kit untuk mendapatkan akses premium!</p>
            </div>

            <div class="mb-6 p-4 bg-[var(--cta-accent)]/10 rounded-lg border border-[var(--cta-accent)]/30">
                <div class="flex items-start gap-3">
                    <i data-feather="info" class="w-5 h-5 text-[var(--cta-accent)] mt-0.5"></i>
                    <div>
                        <h4 class="font-label text-sm text-[var(--cta-accent)] mb-1">Format Kode:</h4>
                        <p class="text-xs text-[var(--text-secondary)]">NAMA-KIT-KODE</p>
                        <p class="text-xs text-[var(--cta-accent)] mt-1">Contoh: ROBOT-NOLEP-ABC123</p>
                    </div>
                </div>
            </div>

            <form id="activation-form" onsubmit="redeemKitCode(event)" class="space-y-6">
                <div class="input-group">
                    <input
                        required
                        type="text"
                        id="kit-code-input"
                        class="input-pro uppercase"
                        placeholder=" "
                        style="text-transform: uppercase;"
                        maxlength="20"
                    >
                    <label class="input-label">Kode Kit (contoh: ROBOT-NOLEP-ABC123)</label>
                </div>

                <div id="code-validation-result" class="hidden">
                    <!-- Hasil validasi akan ditampilkan di sini -->
                </div>

                <button type="submit" class="w-full btn-pro" style="--primary-accent: var(--primary-accent);">
                    <i data-feather="key" class="w-4 h-4 mr-2"></i>
                    Aktivasi Kode Kit
                </button>
            </form>

            <div class="mt-6 pt-4 border-t border-gray-700 text-center">
                <p class="text-xs text-[var(--text-secondary)] mb-2">Belum punya kit?</p>
                <button onclick="closeModal('activation-modal'); showPage('kits-list-page')" class="text-[var(--primary-accent)] hover:underline text-sm">
                    Lihat Koleksi Kit →
                </button>
            </div>
        </div>
    </div>

    <!-- ==================== MODAL GENERATE CODE ======================== -->
    <div id="generate-code-modal" class="modal-container fixed inset-0 z-50 items-center justify-center bg-black bg-opacity-70">
        <div class="glass-card rounded-xl p-8 border border-[var(--primary-accent)] w-full max-w-md relative">
            <button onclick="closeModal('generate-code-modal')" class="absolute top-4 right-4 text-[var(--text-secondary)] hover:text-white">
                <i data-feather="x" class="w-6 h-6"></i>
            </button>

            <div class="text-center mb-6">
                <div class="w-16 h-16 mx-auto mb-4 bg-[var(--primary-accent)]/20 rounded-full flex items-center justify-center">
                    <i data-feather="key" class="w-8 h-8 text-[var(--primary-accent)]"></i>
                </div>
                <h2 class="font-header text-2xl text-[var(--primary-accent)] mb-2">Generate Kode Kit</h2>
                <p class="text-[var(--text-secondary)] text-sm">Buat kode baru untuk akses premium tutorial</p>
            </div>

            <form id="generate-code-form" onsubmit="generateKitCode(event)" class="space-y-6">
                <div>
                    <label class="block font-label text-sm mb-2">Pilih Kit</label>
                    <select id="kit-select" class="w-full p-3 rounded-lg bg-gray-800 border border-gray-600 focus:border-primary-accent focus:outline-none" required>
                        <option value="">Pilih Kit...</option>
                        <option value="robot-nolep">Robot Nolep Kit</option>
                        <option value="weather-station">Weather Station Kit</option>
                        <option value="disco-light">Disco Light Kit</option>
                        <option value="iot-security">IoT Security Kit</option>
                        <option value="smart-garden">Smart Garden Kit</option>
                        <option value="led-matrix">LED Matrix Kit</option>
                    </select>
                </div>

                <div>
                    <label class="block font-label text-sm mb-2">Jumlah Kode</label>
                    <input
                        type="number"
                        id="code-quantity"
                        min="1"
                        max="50"
                        value="1"
                        class="w-full p-3 rounded-lg bg-gray-800 border border-gray-600 focus:border-primary-accent focus:outline-none"
                        required
                    >
                </div>

                <div>
                    <label class="block font-label text-sm mb-2">Catatan (Opsional)</label>
                    <input
                        type="text"
                        id="code-notes"
                        placeholder="Batch untuk Tokopedia..."
                        class="w-full p-3 rounded-lg bg-gray-800 border border-gray-600 focus:border-primary-accent focus:outline-none"
                    >
                </div>

                <button type="submit" class="w-full btn-pro">
                    <i data-feather="plus" class="w-4 h-4 mr-2"></i>
                    Generate Kode
                </button>
            </form>
        </div>
    </div>

    <div id="tutorial-modal" class="modal-container fixed inset-0 z-50 items-center justify-center bg-black bg-opacity-70">
        <div class="glass-card rounded-xl p-8 border border-[var(--secondary-accent)] w-full max-w-lg relative">
            <button onclick="closeModal('tutorial-modal')" class="absolute top-4 right-4 text-[var(--text-secondary)] hover:text-white"><i data-feather="x"></i></button>
            <h2 id="tutorial-modal-title" class="font-tech-mono text-2xl text-[var(--primary-accent)] mb-6 text-center">// Tambah/Edit Tutorial</h2>
            <form id="tutorial-form" class="space-y-6">
                <div class="input-group"><input required type="text" id="tutorial-title" class="input-pro" placeholder=" "><label class="input-label">Judul Tutorial</label></div>
                <div class="input-group"><select id="tutorial-type" class="input-pro"><option value="Gratis">Gratis</option><option value="Premium">Premium</option></select></div>
                <div class="input-group"><select id="tutorial-kit" class="input-pro"></select><label class="input-label">Terkait dengan Kit (opsional)</label></div>
                <button type="submit" class="w-full btn-pro">Simpan</button>
            </form>
        </div>
    </div>
    
    <div id="kit-modal" class="modal-container fixed inset-0 z-50 items-center justify-center bg-black bg-opacity-70">
        <div class="glass-card rounded-xl p-8 border border-[var(--secondary-accent)] w-full max-w-lg relative">
            <button onclick="closeModal('kit-modal')" class="absolute top-4 right-4 text-[var(--text-secondary)] hover:text-white"><i data-feather="x"></i></button>
            <h2 id="kit-modal-title" class="font-tech-mono text-2xl text-[var(--primary-accent)] mb-6 text-center">// Tambah/Edit Kit</h2>
            <form id="kit-form" class="space-y-6">
                <div class="input-group"><input required type="text" id="kit-name" class="input-pro" placeholder=" "><label class="input-label">Nama Kit</label></div>
                <div class="input-group"><input required type="text" id="kit-price" class="input-pro" placeholder=" "><label class="input-label">Harga</label></div>
                <div class="input-group"><input type="text" id="kit-tutorials" class="input-pro" placeholder=" "><label class="input-label">Akses Tutorial (pisahkan dengan koma)</label></div>
                <button type="submit" class="w-full btn-pro">Simpan</button>
            </form>
        </div>
    </div>
    
    <div id="user-modal" class="modal-container fixed inset-0 z-50 items-center justify-center bg-black bg-opacity-70">
        <div class="glass-card rounded-xl p-8 border border-[var(--secondary-accent)] w-full max-w-lg relative">
            <button onclick="closeModal('user-modal')" class="absolute top-4 right-4 text-[var(--text-secondary)] hover:text-white"><i data-feather="x"></i></button>
            <h2 id="user-modal-title" class="font-tech-mono text-2xl text-[var(--primary-accent)] mb-6 text-center">// Edit Pengguna</h2>
            <form id="user-form" class="space-y-6">
                <div class="input-group"><input required type="text" id="user-username" class="input-pro" placeholder=" "><label class="input-label">Username</label></div>
                <div class="input-group"><input required type="email" id="user-email" class="input-pro" placeholder=" "><label class="input-label">Email</label></div>
                <div class="input-group"><select id="user-role" class="input-pro"><option value="Member">Member</option><option value="Admin">Admin</option></select></div>
                <button type="submit" class="w-full btn-pro">Simpan</button>
            </form>
        </div>
    </div>


    <!-- Prism.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>

    <script>
        // Simple client-side router
        const pages = document.querySelectorAll('.page');
        const loader = document.getElementById('loader');

        const showPage = (pageId, context = {}) => {
            loader.style.display = 'flex'; // Tampilkan loader

            setTimeout(() => { // Beri sedikit waktu agar loader terlihat
                if (!pageId || !document.getElementById(pageId)) {
                    pageId = 'landing-page'; // Fallback ke landing page
                }

                pages.forEach(page => {
                    page.classList.remove('active');
                    if(page.style.display !== 'none') page.style.display = 'none';
                });
                
                const activePage = document.getElementById(pageId);
                activePage.classList.add('active');
                const flexPages = ['login-page', 'register-page', 'forgot-password-page', 'member-area-page', 'tutorial-player-page', 'account-settings-page', 'kit-detail-page', 'tutorial-preview-page', 'admin-page'];
                if (flexPages.includes(pageId)) {
                    activePage.style.display = 'flex';
                } else {
                    activePage.style.display = 'block';
                }

                // Dynamic content for kit-detail-page header
                if (pageId === 'kit-detail-page') {
                    const backButton = document.getElementById('kit-detail-back-button');
                    const backText = document.getElementById('kit-detail-back-text');
                    const actionButtonContainer = document.getElementById('kit-detail-action-button-container');

                    if (context.from === 'landing-page') {
                        backButton.onclick = () => showPage('landing-page');
                        backText.textContent = 'Kembali ke Beranda';
                        actionButtonContainer.innerHTML = `<a href="#" onclick="showPage('login-page')" class="px-4 py-2 rounded-lg text-sm font-label border-2 border-[var(--primary-accent)] text-[var(--primary-accent)] hover:bg-[var(--primary-accent)] hover:text-black transition-colors">LOGIN</a>`;
                    } else if (context.from === 'kits-list-page') {
                        backButton.onclick = () => showPage('kits-list-page');
                        backText.textContent = 'Kembali ke Etalase';
                        actionButtonContainer.innerHTML = `<a href="#" onclick="showPage('login-page')" class="px-4 py-2 rounded-lg text-sm font-label border-2 border-[var(--primary-accent)] text-[var(--primary-accent)] hover:bg-[var(--primary-accent)] hover:text-black transition-colors">LOGIN</a>`;
                    } else {
                        backButton.onclick = () => showPage('member-area-page');
                        backText.textContent = 'Back to Dashboard';
                        actionButtonContainer.innerHTML = `<a href="#" onclick="showPage('landing-page')" class="px-4 py-2 rounded-lg text-sm font-label border-2 border-[var(--primary-accent)] text-[var(--primary-accent)] hover:bg-[var(--primary-accent)] hover:text-black transition-colors">LOGOUT</a>`;
                    }
                }

                // Dynamic content for tutorial-preview-page
                if (pageId === 'tutorial-preview-page') {
                    document.getElementById('preview-title').textContent = context.title || 'Judul Tutorial';
                    const ctaHeading = document.getElementById('locked-cta-heading');
                    const ctaText = document.getElementById('locked-cta-text');
                    const ctaButtons = document.getElementById('locked-cta-buttons');
                    
                    if (context.isPremium) {
                        ctaHeading.textContent = 'Konten Premium Terkunci';
                        ctaText.textContent = 'Buka video premium ini dengan membeli kode akses.';
                        ctaButtons.innerHTML = `
                            <button onclick="buyPremiumCode()" class="btn-pro w-full sm:w-auto" style="--primary-accent: var(--cta-accent);">Beli Kode Akses</button>
                        `;
                    } else {
                        ctaHeading.textContent = 'Buka Konten Lengkap';
                        ctaText.textContent = 'Dapatkan akses ke video, sisa artikel, kode sumber, dan forum diskusi.';
                        ctaButtons.innerHTML = `
                            <button onclick="showPage('login-page')" class="btn-pro w-full sm:w-auto">Login untuk Lanjutkan</button>
                            <button onclick="showPage('register-page')" class="btn-pro w-full sm:w-auto" style="--primary-accent: var(--secondary-accent);">Buat Akun</button>
                        `;
                    }
                }

                feather.replace();
                if (typeof Prism !== 'undefined') {
                    Prism.highlightAll();
                }
                window.scrollTo(0, 0);
                loader.style.display = 'none'; // Sembunyikan loader
            }, 300); // Durasi loader
        };

        // Modal Logic
        const openModal = (modalId) => {
            document.getElementById(modalId).classList.add('active');
            feather.replace();
        };
        const closeModal = (modalId) => {
            document.getElementById(modalId).classList.remove('active');
        };









    </script>

    <!-- Supabase CDN -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>

    <!-- Application Scripts -->
    <script src="deployment-config.js"></script>
    <script src="supabase-config.js"></script>
    <script src="app.js"></script>
    <script src="authentication.js"></script>
    <script src="kit-tutorial-system.js"></script>
    <script src="admin-functions.js"></script>
    <script src="progress-tracking.js"></script>

    <script src="https://unpkg.com/feather-icons"></script>
    <script>
        feather.replace();
    </script>
</body>
</html>
