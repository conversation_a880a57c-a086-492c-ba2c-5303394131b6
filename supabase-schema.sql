-- ==================== TECHNO-REBEL IOT KIT PLATFORM DATABASE SCHEMA ========================
-- Supabase Database Schema for Kit-Tutorial Premium Access System
-- Created: 2025-08-01

-- Enable Row Level Security
ALTER DATABASE postgres SET "app.jwt_secret" TO 'your-jwt-secret-here';

-- ==================== USERS TABLE (extends auth.users) ========================
CREATE TABLE public.user_profiles (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    full_name VARCHAR(100),
    avatar_url TEXT,
    role VARCHAR(20) DEFAULT 'member' CHECK (role IN ('admin', 'member')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ==================== KITS TABLE ========================
CREATE TABLE public.kits (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    price DECIMAL(10,2),
    image_url TEXT,
    difficulty_level VARCHAR(20) DEFAULT 'beginner' CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced')),
    components JSONB, -- Array of components included
    learning_outcomes JSONB, -- Array of what users will learn
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ==================== TUTORIALS TABLE ========================
CREATE TABLE public.tutorials (
    id VARCHAR(50) PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    duration_minutes INTEGER,
    video_url TEXT,
    thumbnail_url TEXT,
    difficulty_level VARCHAR(20) DEFAULT 'beginner' CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced')),
    is_premium BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    order_index INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ==================== KIT-TUTORIAL RELATIONSHIPS ========================
CREATE TABLE public.kit_tutorials (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    kit_id VARCHAR(50) REFERENCES public.kits(id) ON DELETE CASCADE,
    tutorial_id VARCHAR(50) REFERENCES public.tutorials(id) ON DELETE CASCADE,
    is_included_free BOOLEAN DEFAULT true, -- Tutorial included free with kit purchase
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(kit_id, tutorial_id)
);

-- ==================== REDEMPTION CODES TABLE ========================
CREATE TABLE public.redemption_codes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    code VARCHAR(50) UNIQUE NOT NULL,
    kit_id VARCHAR(50) REFERENCES public.kits(id) ON DELETE CASCADE,
    is_used BOOLEAN DEFAULT false,
    used_by UUID REFERENCES public.user_profiles(id) ON DELETE SET NULL,
    used_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    notes TEXT,
    created_by UUID REFERENCES public.user_profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ==================== USER KIT OWNERSHIP ========================
CREATE TABLE public.user_kits (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    kit_id VARCHAR(50) REFERENCES public.kits(id) ON DELETE CASCADE,
    acquired_via VARCHAR(20) DEFAULT 'code' CHECK (acquired_via IN ('code', 'purchase', 'admin')),
    redemption_code_id UUID REFERENCES public.redemption_codes(id) ON DELETE SET NULL,
    acquired_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, kit_id)
);

-- ==================== USER TUTORIAL ACCESS ========================
CREATE TABLE public.user_tutorial_access (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    tutorial_id VARCHAR(50) REFERENCES public.tutorials(id) ON DELETE CASCADE,
    access_type VARCHAR(20) DEFAULT 'kit' CHECK (access_type IN ('kit', 'purchase', 'admin')),
    kit_id VARCHAR(50) REFERENCES public.kits(id) ON DELETE SET NULL, -- If access via kit
    granted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, tutorial_id)
);

-- ==================== USER TUTORIAL PROGRESS ========================
CREATE TABLE public.user_tutorial_progress (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    tutorial_id VARCHAR(50) REFERENCES public.tutorials(id) ON DELETE CASCADE,
    progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
    completed_at TIMESTAMP WITH TIME ZONE,
    last_watched_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    watch_time_seconds INTEGER DEFAULT 0,
    UNIQUE(user_id, tutorial_id)
);

-- ==================== INDEXES FOR PERFORMANCE ========================
CREATE INDEX idx_user_profiles_username ON public.user_profiles(username);
CREATE INDEX idx_user_profiles_role ON public.user_profiles(role);
CREATE INDEX idx_kits_active ON public.kits(is_active);
CREATE INDEX idx_tutorials_premium ON public.tutorials(is_premium);
CREATE INDEX idx_tutorials_active ON public.tutorials(is_active);
CREATE INDEX idx_redemption_codes_code ON public.redemption_codes(code);
CREATE INDEX idx_redemption_codes_used ON public.redemption_codes(is_used);
CREATE INDEX idx_redemption_codes_kit ON public.redemption_codes(kit_id);
CREATE INDEX idx_user_kits_user ON public.user_kits(user_id);
CREATE INDEX idx_user_kits_kit ON public.user_kits(kit_id);
CREATE INDEX idx_user_tutorial_access_user ON public.user_tutorial_access(user_id);
CREATE INDEX idx_user_tutorial_progress_user ON public.user_tutorial_progress(user_id);

-- ==================== ROW LEVEL SECURITY POLICIES ========================

-- Enable RLS on all tables
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.kits ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tutorials ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.kit_tutorials ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.redemption_codes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_kits ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_tutorial_access ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_tutorial_progress ENABLE ROW LEVEL SECURITY;

-- User Profiles Policies
CREATE POLICY "Users can view their own profile" ON public.user_profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON public.user_profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Admins can view all profiles" ON public.user_profiles
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.user_profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Kits Policies (Public read, admin write)
CREATE POLICY "Anyone can view active kits" ON public.kits
    FOR SELECT USING (is_active = true);

CREATE POLICY "Admins can manage kits" ON public.kits
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.user_profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Tutorials Policies (Public read, admin write)
CREATE POLICY "Anyone can view active tutorials" ON public.tutorials
    FOR SELECT USING (is_active = true);

CREATE POLICY "Admins can manage tutorials" ON public.tutorials
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.user_profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Kit-Tutorial Relationships (Public read, admin write)
CREATE POLICY "Anyone can view kit-tutorial relationships" ON public.kit_tutorials
    FOR SELECT USING (true);

CREATE POLICY "Admins can manage kit-tutorial relationships" ON public.kit_tutorials
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.user_profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Redemption Codes Policies (Admin only)
CREATE POLICY "Admins can manage redemption codes" ON public.redemption_codes
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.user_profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- User Kits Policies
CREATE POLICY "Users can view their own kits" ON public.user_kits
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own kit ownership" ON public.user_kits
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Admins can manage all user kits" ON public.user_kits
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.user_profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- User Tutorial Access Policies
CREATE POLICY "Users can view their own tutorial access" ON public.user_tutorial_access
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own tutorial access" ON public.user_tutorial_access
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Admins can manage all tutorial access" ON public.user_tutorial_access
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.user_profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- User Tutorial Progress Policies
CREATE POLICY "Users can manage their own progress" ON public.user_tutorial_progress
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Admins can view all progress" ON public.user_tutorial_progress
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.user_profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- ==================== FUNCTIONS AND TRIGGERS ========================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON public.user_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_kits_updated_at BEFORE UPDATE ON public.kits FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tutorials_updated_at BEFORE UPDATE ON public.tutorials FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to handle user registration
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.user_profiles (id, username, full_name, avatar_url)
    VALUES (
        NEW.id,
        COALESCE(NEW.raw_user_meta_data->>'username', NEW.email),
        COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
        COALESCE(NEW.raw_user_meta_data->>'avatar_url', '')
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for new user registration
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
