// ==================== ADMIN FUNCTIONS ========================
// Admin management functions for the IoT Kit Platform
// Created: 2025-08-01

// ==================== ADMIN MODAL FUNCTIONS ========================

// Delete Item Function
const deleteItem = (el) => {
    if (confirm('Yakin mau nge-delete item ini? Gak bisa di-undo lho.')) {
        el.closest('tr').remove();
        alert('Item berhasil dihapus. RIP.');
    }
};

// Populate Kit Dropdown
const populateKitDropdown = () => {
    const kitSelect = document.getElementById('tutorial-kit');
    kitSelect.innerHTML = '<option value="">- Tidak Terkait -</option>'; // Clear existing options and add a default
    
    const kitTableRows = document.querySelectorAll('#kit-table-body tr');
    kitTableRows.forEach(row => {
        const kitName = row.cells[0].textContent;
        const option = document.createElement('option');
        option.value = kitName;
        option.textContent = kitName;
        kitSelect.appendChild(option);
    });
};

// Open Tutorial Modal
const openTutorialModal = (isEditing = false, el = null) => {
    populateKitDropdown();
    const titleEl = document.getElementById('tutorial-modal-title');
    const form = document.getElementById('tutorial-form');
    form.reset();

    if (isEditing && el) {
        titleEl.textContent = '// Edit Tutorial';
        const row = el.closest('tr');
        document.getElementById('tutorial-title').value = row.cells[0].textContent;
        document.getElementById('tutorial-type').value = row.cells[1].querySelector('span').textContent;
        document.getElementById('tutorial-kit').value = row.cells[2].textContent === '-' ? '' : row.cells[2].textContent;
    } else {
        titleEl.textContent = '// Tambah Tutorial Baru';
    }
    openModal('tutorial-modal');
};

// Open Kit Modal
const openKitModal = (isEditing = false, el = null) => {
    const titleEl = document.getElementById('kit-modal-title');
    const form = document.getElementById('kit-form');
    form.reset();
    if (isEditing && el) {
        titleEl.textContent = '// Edit Kit';
        const row = el.closest('tr');
        document.getElementById('kit-name').value = row.cells[0].textContent;
        document.getElementById('kit-price').value = row.cells[1].textContent;
        document.getElementById('kit-tutorials').value = row.cells[2].textContent;
    } else {
        titleEl.textContent = '// Tambah Kit Baru';
    }
    openModal('kit-modal');
};

// Open User Modal
const openUserModal = (isEditing = false, el = null) => {
    const titleEl = document.getElementById('user-modal-title');
    const form = document.getElementById('user-form');
    form.reset();
    if (isEditing && el) {
        titleEl.textContent = '// Edit Pengguna';
        const row = el.closest('tr');
        document.getElementById('user-username').value = row.cells[0].textContent;
        document.getElementById('user-email').value = row.cells[1].textContent;
        document.getElementById('user-role').value = row.cells[2].querySelector('span').textContent;
    } else {
         titleEl.textContent = '// Tambah Pengguna Baru';
    }
    openModal('user-modal');
};

// ==================== FORM HANDLERS ========================

// Handle Tutorial Form Submission
const handleTutorialForm = (event) => {
    event.preventDefault();
    alert('Data tutorial berhasil di-update!');
    closeModal('tutorial-modal');
};

// Handle Kit Form Submission
const handleKitForm = (event) => {
    event.preventDefault();
    alert('Data kit berhasil di-update!');
    closeModal('kit-modal');
};

// Handle User Form Submission
const handleUserForm = (event) => {
    event.preventDefault();
    alert('Data user berhasil di-update!');
    closeModal('user-modal');
};

// Handle Settings Form Submission
const handleSettingsForm = (event) => {
    event.preventDefault();
    alert('Wih, GG! Pengaturan berhasil disimpan.');
};

// ==================== LEGACY FUNCTIONS ========================

// Mobile Menu Toggle
const toggleMobileMenu = () => {
    const mobileMenu = document.getElementById('mobile-menu');
    if (mobileMenu) {
        mobileMenu.classList.toggle('active');
    }
};

// Buy Premium Code Function
const buyPremiumCode = () => {
    const message = encodeURIComponent("Halo! Saya mau beli kode akses premium untuk tutorial. Berapa harganya?");
    const whatsappUrl = `https://wa.me/6281234567890?text=${message}`;
    window.open(whatsappUrl, '_blank');
};

// Language Switcher
const setLanguage = (lang) => {
    document.documentElement.lang = lang;
    document.querySelectorAll('[data-lang-id]').forEach(el => {
        const text = lang === 'id' ? el.getAttribute('data-lang-id') : el.getAttribute('data-lang-en');
        el.innerHTML = text; // Use innerHTML to support span tags in headlines
    });
    if (typeof feather !== 'undefined') {
        feather.replace(); // Re-render icons if any text changes affect them
    }
};

// ==================== FILTER AND SEARCH FUNCTIONS ========================

// Projects Filter and Search Functions
const filterProjects = () => {
    const searchTerm = document.getElementById('project-search').value.toLowerCase();
    const categoryFilter = document.getElementById('project-filter').value;
    const priceFilter = document.getElementById('price-filter').value;
    const projectCards = document.querySelectorAll('.project-card');
    const noResults = document.getElementById('no-results');
    let visibleCount = 0;

    projectCards.forEach(card => {
        const title = card.querySelector('h3').textContent.toLowerCase();
        const description = card.querySelector('p').textContent.toLowerCase();
        const category = card.getAttribute('data-category');
        const price = parseInt(card.getAttribute('data-price'));

        let matchesSearch = title.includes(searchTerm) || description.includes(searchTerm);
        let matchesCategory = categoryFilter === 'all' || category === categoryFilter;
        let matchesPrice = true;

        if (priceFilter === 'low') matchesPrice = price < 300000;
        else if (priceFilter === 'medium') matchesPrice = price >= 300000 && price <= 500000;
        else if (priceFilter === 'high') matchesPrice = price > 500000;

        if (matchesSearch && matchesCategory && matchesPrice) {
            card.style.display = 'block';
            visibleCount++;
        } else {
            card.style.display = 'none';
        }
    });

    if (noResults) {
        noResults.style.display = visibleCount === 0 ? 'block' : 'none';
    }
};

// Tutorials Filter and Search Functions
const filterTutorials = (type) => {
    const tutorialCards = document.querySelectorAll('.tutorial-card');
    const filterButtons = document.querySelectorAll('.tutorial-filter-btn');
    const noResults = document.getElementById('no-tutorial-results');
    let visibleCount = 0;

    // Update active filter button
    filterButtons.forEach(btn => {
        btn.classList.remove('bg-[var(--primary-accent)]', 'text-black');
        btn.classList.add('text-[var(--text-secondary)]');
    });
    
    const activeButton = document.getElementById(`filter-${type}`);
    if (activeButton) {
        activeButton.classList.add('bg-[var(--primary-accent)]', 'text-black');
        activeButton.classList.remove('text-[var(--text-secondary)]');
    }

    tutorialCards.forEach(card => {
        const cardType = card.getAttribute('data-type');
        if (type === 'all' || cardType === type) {
            card.style.display = 'block';
            visibleCount++;
        } else {
            card.style.display = 'none';
        }
    });

    if (noResults) {
        noResults.style.display = visibleCount === 0 ? 'block' : 'none';
    }
};

const searchTutorials = () => {
    const searchTerm = document.getElementById('tutorial-search').value.toLowerCase();
    const tutorialCards = document.querySelectorAll('.tutorial-card');
    const noResults = document.getElementById('no-tutorial-results');
    let visibleCount = 0;

    tutorialCards.forEach(card => {
        const title = card.querySelector('h3').textContent.toLowerCase();
        const description = card.querySelector('p').textContent.toLowerCase();

        if (title.includes(searchTerm) || description.includes(searchTerm)) {
            card.style.display = 'block';
            visibleCount++;
        } else {
            card.style.display = 'none';
        }
    });

    if (noResults) {
        noResults.style.display = visibleCount === 0 ? 'block' : 'none';
    }
};

// ==================== INITIALIZATION ========================

// Setup Legacy Form Event Listeners
const setupLegacyForms = () => {
    // Tutorial form
    const tutorialForm = document.getElementById('tutorial-form');
    if (tutorialForm) {
        tutorialForm.addEventListener('submit', handleTutorialForm);
    }
    
    // Kit form
    const kitForm = document.getElementById('kit-form');
    if (kitForm) {
        kitForm.addEventListener('submit', handleKitForm);
    }
    
    // User form
    const userForm = document.getElementById('user-form');
    if (userForm) {
        userForm.addEventListener('submit', handleUserForm);
    }
    
    // Settings form
    const settingsForm = document.getElementById('settings-form');
    if (settingsForm) {
        settingsForm.addEventListener('submit', handleSettingsForm);
    }
};

// Initialize Legacy Functions
const initializeLegacyFunctions = () => {
    // Auto-detect language
    const userLang = navigator.language || navigator.userLanguage;
    if (userLang.startsWith('id')) {
        setLanguage('id');
    } else {
        setLanguage('en');
    }
    
    // Show landing page by default
    showPage('landing-page');
    
    // Show default tutorial tab
    showTutorialTab('deskripsi-content');
    
    // Setup forms
    setupLegacyForms();
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    initializeLegacyFunctions();
});
