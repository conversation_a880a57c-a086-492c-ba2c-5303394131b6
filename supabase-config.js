// ==================== SUPABASE CONFIGURATION ========================
// Supabase client configuration for Techno-Rebel IoT Kit Platform
// Created: 2025-08-01

// Supabase Configuration
const SUPABASE_CONFIG = {
    url: 'YOUR_SUPABASE_URL', // Replace with your Supabase project URL
    anonKey: 'YOUR_SUPABASE_ANON_KEY', // Replace with your Supabase anon key
    options: {
        auth: {
            autoRefreshToken: true,
            persistSession: true,
            detectSessionInUrl: true
        }
    }
};

// Initialize Supabase client (will be loaded from CDN in production)
let supabase;

// Initialize Supabase when script loads
const initSupabase = () => {
    if (typeof window !== 'undefined' && window.supabase) {
        supabase = window.supabase.createClient(
            SUPABASE_CONFIG.url,
            SUPABASE_CONFIG.anonKey,
            SUPABASE_CONFIG.options
        );
        console.log('Supabase initialized successfully');
        return true;
    }
    console.warn('Supabase not loaded yet');
    return false;
};

// ==================== AUTHENTICATION FUNCTIONS ========================

// Sign up new user
const signUpUser = async (email, password, userData = {}) => {
    try {
        const { data, error } = await supabase.auth.signUp({
            email,
            password,
            options: {
                data: {
                    username: userData.username || email.split('@')[0],
                    full_name: userData.fullName || '',
                    avatar_url: userData.avatarUrl || ''
                }
            }
        });

        if (error) throw error;
        return { success: true, data };
    } catch (error) {
        console.error('Sign up error:', error);
        return { success: false, error: error.message };
    }
};

// Sign in user
const signInUser = async (email, password) => {
    try {
        const { data, error } = await supabase.auth.signInWithPassword({
            email,
            password
        });

        if (error) throw error;
        return { success: true, data };
    } catch (error) {
        console.error('Sign in error:', error);
        return { success: false, error: error.message };
    }
};

// Sign out user
const signOutUser = async () => {
    try {
        const { error } = await supabase.auth.signOut();
        if (error) throw error;
        return { success: true };
    } catch (error) {
        console.error('Sign out error:', error);
        return { success: false, error: error.message };
    }
};

// Get current user
const getCurrentUser = async () => {
    try {
        const { data: { user }, error } = await supabase.auth.getUser();
        if (error) throw error;
        return { success: true, user };
    } catch (error) {
        console.error('Get user error:', error);
        return { success: false, error: error.message };
    }
};

// ==================== KIT MANAGEMENT FUNCTIONS ========================

// Get all active kits
const getKits = async () => {
    try {
        const { data, error } = await supabase
            .from('kits')
            .select('*')
            .eq('is_active', true)
            .order('created_at', { ascending: false });

        if (error) throw error;
        return { success: true, data };
    } catch (error) {
        console.error('Get kits error:', error);
        return { success: false, error: error.message };
    }
};

// Get kit by ID with tutorials
const getKitWithTutorials = async (kitId) => {
    try {
        const { data, error } = await supabase
            .from('kits')
            .select(`
                *,
                kit_tutorials (
                    tutorial_id,
                    is_included_free,
                    tutorials (*)
                )
            `)
            .eq('id', kitId)
            .eq('is_active', true)
            .single();

        if (error) throw error;
        return { success: true, data };
    } catch (error) {
        console.error('Get kit with tutorials error:', error);
        return { success: false, error: error.message };
    }
};

// ==================== TUTORIAL MANAGEMENT FUNCTIONS ========================

// Get all active tutorials
const getTutorials = async () => {
    try {
        const { data, error } = await supabase
            .from('tutorials')
            .select('*')
            .eq('is_active', true)
            .order('order_index', { ascending: true });

        if (error) throw error;
        return { success: true, data };
    } catch (error) {
        console.error('Get tutorials error:', error);
        return { success: false, error: error.message };
    }
};

// Get user's accessible tutorials
const getUserTutorials = async (userId) => {
    try {
        const { data, error } = await supabase
            .from('user_tutorial_access')
            .select(`
                *,
                tutorials (*),
                user_tutorial_progress (*)
            `)
            .eq('user_id', userId);

        if (error) throw error;
        return { success: true, data };
    } catch (error) {
        console.error('Get user tutorials error:', error);
        return { success: false, error: error.message };
    }
};

// ==================== REDEMPTION CODE FUNCTIONS ========================

// Validate and redeem kit code
const redeemKitCodeSupabase = async (code, userId) => {
    try {
        // Start transaction
        const { data: codeData, error: codeError } = await supabase
            .from('redemption_codes')
            .select(`
                *,
                kits (*),
                kit_tutorials (
                    tutorial_id,
                    tutorials (*)
                )
            `)
            .eq('code', code.toUpperCase())
            .eq('is_used', false)
            .single();

        if (codeError || !codeData) {
            return { success: false, message: 'Kode tidak valid atau sudah digunakan!' };
        }

        // Check if code is expired
        if (codeData.expires_at && new Date(codeData.expires_at) < new Date()) {
            return { success: false, message: 'Kode sudah kadaluarsa!' };
        }

        // Mark code as used
        const { error: updateError } = await supabase
            .from('redemption_codes')
            .update({
                is_used: true,
                used_by: userId,
                used_at: new Date().toISOString()
            })
            .eq('id', codeData.id);

        if (updateError) throw updateError;

        // Add kit ownership
        const { error: kitError } = await supabase
            .from('user_kits')
            .insert({
                user_id: userId,
                kit_id: codeData.kit_id,
                acquired_via: 'code',
                redemption_code_id: codeData.id
            });

        if (kitError && !kitError.message.includes('duplicate')) {
            throw kitError;
        }

        // Add tutorial access for included tutorials
        const tutorialAccess = codeData.kit_tutorials
            .filter(kt => kt.is_included_free)
            .map(kt => ({
                user_id: userId,
                tutorial_id: kt.tutorial_id,
                access_type: 'kit',
                kit_id: codeData.kit_id
            }));

        if (tutorialAccess.length > 0) {
            const { error: accessError } = await supabase
                .from('user_tutorial_access')
                .insert(tutorialAccess);

            if (accessError && !accessError.message.includes('duplicate')) {
                throw accessError;
            }
        }

        return {
            success: true,
            kitId: codeData.kit_id,
            kitName: codeData.kits.name,
            tutorials: codeData.kit_tutorials
                .filter(kt => kt.is_included_free)
                .map(kt => kt.tutorials)
        };

    } catch (error) {
        console.error('Redeem code error:', error);
        return { success: false, message: 'Terjadi kesalahan sistem. Silakan coba lagi.' };
    }
};

// Generate new redemption codes (Admin only)
const generateRedemptionCodes = async (kitId, quantity, notes = '') => {
    try {
        const codes = [];
        const kitPrefix = getKitPrefix(kitId);

        for (let i = 0; i < quantity; i++) {
            const randomCode = generateRandomCode();
            const fullCode = `${kitPrefix}-${randomCode}`;
            codes.push({
                code: fullCode,
                kit_id: kitId,
                notes: notes,
                created_by: (await getCurrentUser()).user?.id
            });
        }

        const { data, error } = await supabase
            .from('redemption_codes')
            .insert(codes)
            .select();

        if (error) throw error;
        return { success: true, data };
    } catch (error) {
        console.error('Generate codes error:', error);
        return { success: false, error: error.message };
    }
};

// Get redemption codes (Admin only)
const getRedemptionCodes = async () => {
    try {
        const { data, error } = await supabase
            .from('redemption_codes')
            .select(`
                *,
                kits (name),
                user_profiles!redemption_codes_used_by_fkey (username)
            `)
            .order('created_at', { ascending: false });

        if (error) throw error;
        return { success: true, data };
    } catch (error) {
        console.error('Get redemption codes error:', error);
        return { success: false, error: error.message };
    }
};

// ==================== USER PROGRESS FUNCTIONS ========================

// Update tutorial progress
const updateTutorialProgress = async (userId, tutorialId, progressData) => {
    try {
        const { data, error } = await supabase
            .from('user_tutorial_progress')
            .upsert({
                user_id: userId,
                tutorial_id: tutorialId,
                progress_percentage: progressData.percentage || 0,
                watch_time_seconds: progressData.watchTime || 0,
                last_watched_at: new Date().toISOString(),
                ...(progressData.percentage === 100 && { completed_at: new Date().toISOString() })
            });

        if (error) throw error;
        return { success: true, data };
    } catch (error) {
        console.error('Update progress error:', error);
        return { success: false, error: error.message };
    }
};

// Get user dashboard data
const getUserDashboardData = async (userId) => {
    try {
        // Get user kits with progress
        const { data: userKits, error: kitsError } = await supabase
            .from('user_kits')
            .select(`
                *,
                kits (*),
                user_tutorial_access!inner (
                    tutorial_id,
                    tutorials (*),
                    user_tutorial_progress (*)
                )
            `)
            .eq('user_id', userId);

        if (kitsError) throw kitsError;

        // Get user tutorial progress summary
        const { data: progressSummary, error: progressError } = await supabase
            .from('user_tutorial_progress')
            .select('progress_percentage, completed_at')
            .eq('user_id', userId);

        if (progressError) throw progressError;

        return { success: true, data: { userKits, progressSummary } };
    } catch (error) {
        console.error('Get dashboard data error:', error);
        return { success: false, error: error.message };
    }
};

// ==================== UTILITY FUNCTIONS ========================

// Helper function to get kit prefix (same as in main JS)
const getKitPrefix = (kitId) => {
    const prefixes = {
        'robot-nolep': 'ROBOT-NOLEP',
        'weather-station': 'WEATHER-STN',
        'disco-light': 'DISCO-LIGHT',
        'iot-security': 'IOT-SEC',
        'smart-garden': 'SMART-GARDEN',
        'led-matrix': 'LED-MATRIX'
    };
    return prefixes[kitId] || 'UNKNOWN-KIT';
};

// Helper function to generate random code (same as in main JS)
const generateRandomCode = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 6; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
};

// Initialize Supabase when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Try to initialize Supabase
    if (!initSupabase()) {
        // If Supabase CDN not loaded yet, try again after a delay
        setTimeout(initSupabase, 1000);
    }
});
