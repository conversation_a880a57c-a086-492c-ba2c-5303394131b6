-- ==================== SAMPLE DATA FOR TECHNO-REBEL IOT KIT PLATFORM ========================
-- Sample data for testing the kit-tutorial premium access system
-- Created: 2025-08-01

-- ==================== SAMPLE KITS ========================
INSERT INTO public.kits (id, name, description, price, image_url, difficulty_level, components, learning_outcomes, is_active) VALUES
('robot-nolep', 'Robot Nolep Kit', 'Rakit robot yang jago ghosting tembok dan rintangan lain. *No cap*, dia lebih jago jaga jarak daripada lo. Cocok buat pemula yang mau langsung liat hasil nyata.', 350000.00, 'https://placehold.co/800x600/120D21/EAEAEA?text=Robot+Nolep+Kit', 'beginner', 
'["1x ESP32 Trainer Board", "1x OLED Display SSD1306", "1x Sensor Suhu & Kelembaban DHT11", "3x Push Button", "1x Potentiometer", "1x Kabel USB-C", "1x Panduan Lengkap"]'::jsonb,
'["Dasar-dasar ESP32 dan IoT", "Sensor suhu dan kelembaban", "Display OLED programming", "WiFi connectivity", "Web server basics"]'::jsonb, true),

('weather-station', 'Weather Station Kit', 'Bikin stasiun cuaca pribadi yang bisa monitoring suhu, kelembaban, tekanan udara. Perfect buat yang suka data dan prediksi cuaca.', 425000.00, 'https://placehold.co/800x600/120D21/9B5DE5?text=Weather+Station', 'intermediate',
'["1x ESP32 DevKit", "1x BME280 Sensor", "1x OLED Display", "1x Real Time Clock", "1x SD Card Module", "1x Solar Panel", "1x Battery Pack"]'::jsonb,
'["Advanced sensor integration", "Data logging systems", "Solar power management", "Real-time data visualization", "Weather prediction algorithms"]'::jsonb, true),

('disco-light', 'Disco Light Kit', 'LED strip yang bisa dancing sesuai beat musik. Bikin party lo jadi lebih hype dengan light show yang epic!', 275000.00, 'https://placehold.co/800x600/120D21/FEE440?text=Disco+Light', 'beginner',
'["1x ESP32 Board", "5m WS2812B LED Strip", "1x Microphone Module", "1x Power Supply", "1x Acrylic Case", "1x Remote Control"]'::jsonb,
'["LED strip programming", "Audio processing", "FFT analysis", "Color theory", "Music visualization"]'::jsonb, true),

('iot-security', 'IoT Security Kit', 'Sistem keamanan rumah dengan kamera, sensor gerak, dan notifikasi real-time. Jaga rumah lo dengan teknologi canggih.', 650000.00, 'https://placehold.co/800x600/120D21/00F5D4?text=Security+Kit', 'advanced',
'["1x ESP32-CAM", "1x PIR Motion Sensor", "1x Door/Window Sensor", "1x Buzzer", "1x Relay Module", "1x Power Adapter", "1x Mounting Kit"]'::jsonb,
'["Camera integration", "Motion detection", "Security protocols", "Real-time notifications", "Home automation"]'::jsonb, true),

('smart-garden', 'Smart Garden Kit', 'Otomatis siram tanaman, monitor kelembaban tanah, dan kontrol pencahayaan. Perfect buat yang pengen berkebun tapi sering lupa.', 380000.00, 'https://placehold.co/800x600/120D21/9B5DE5?text=Smart+Garden', 'intermediate',
'["1x ESP32 Board", "1x Soil Moisture Sensor", "1x Water Pump", "1x Light Sensor", "1x LED Grow Light", "1x Relay Board", "1x Tubing Kit"]'::jsonb,
'["Agricultural IoT", "Sensor automation", "Plant care systems", "Environmental monitoring", "Sustainable technology"]'::jsonb, true),

('led-matrix', 'LED Matrix Kit', 'Display matrix 32x32 yang bisa nampilin teks, animasi, dan grafik. Bikin project display yang eye-catching!', 320000.00, 'https://placehold.co/800x600/120D21/FEE440?text=LED+Matrix', 'intermediate',
'["1x ESP32 Board", "1x 32x32 LED Matrix", "1x Matrix Driver", "1x Power Supply", "1x Frame Kit", "1x Button Panel"]'::jsonb,
'["Matrix display programming", "Graphics rendering", "Animation systems", "Text scrolling", "Visual effects"]'::jsonb, true);

-- ==================== SAMPLE TUTORIALS ========================
INSERT INTO public.tutorials (id, title, description, duration_minutes, video_url, thumbnail_url, difficulty_level, is_premium, order_index) VALUES
-- Robot Nolep Kit Tutorials
('dht11-basics', 'Video 1: Baca Suhu DHT11', 'Pelajari cara membaca data sensor suhu dan kelembaban DHT11 dengan ESP32. Tutorial dasar yang wajib dikuasai.', 15, 'https://youtube.com/watch?v=example1', 'https://placehold.co/600x400/120D21/EAEAEA?text=DHT11+Tutorial', 'beginner', true, 1),
('oled-display', 'Video 2: Tampilkan ke OLED', 'Mengirim data sensor untuk ditampilkan di layar OLED SSD1306. Bikin project lo lebih interaktif.', 22, 'https://youtube.com/watch?v=example2', 'https://placehold.co/600x400/120D21/EAEAEA?text=OLED+Display', 'beginner', true, 2),
('wifi-control', 'Video 3: Kontrol via WiFi', 'Setup web server di ESP32 untuk kontrol remote via WiFi. Level up project lo ke IoT!', 28, 'https://youtube.com/watch?v=example3', 'https://placehold.co/600x400/120D21/EAEAEA?text=WiFi+Control', 'intermediate', true, 3),

-- Weather Station Kit Tutorials
('weather-sensors', 'Weather Sensors Setup', 'Integrasi sensor BME280 untuk monitoring cuaca lengkap dengan kalibrasi yang tepat.', 20, 'https://youtube.com/watch?v=example4', 'https://placehold.co/600x400/120D21/9B5DE5?text=Weather+Sensors', 'intermediate', true, 4),
('data-logging', 'Data Logging System', 'Sistem penyimpanan data cuaca ke SD card dengan timestamp dan backup otomatis.', 25, 'https://youtube.com/watch?v=example5', 'https://placehold.co/600x400/120D21/9B5DE5?text=Data+Logging', 'intermediate', true, 5),

-- IoT Security Kit Tutorials
('security-basics', 'Security Basics', 'Dasar-dasar sistem keamanan IoT, enkripsi, dan protokol komunikasi yang aman.', 18, 'https://youtube.com/watch?v=example6', 'https://placehold.co/600x400/120D21/00F5D4?text=Security+Basics', 'advanced', true, 6),
('camera-integration', 'Camera Integration', 'Integrasi ESP32-CAM untuk streaming video dan capture foto dengan kualitas optimal.', 30, 'https://youtube.com/watch?v=example7', 'https://placehold.co/600x400/120D21/00F5D4?text=Camera+Setup', 'advanced', true, 7),
('motion-detection', 'Motion Detection', 'Algoritma deteksi gerakan dengan PIR sensor dan notifikasi real-time ke smartphone.', 25, 'https://youtube.com/watch?v=example8', 'https://placehold.co/600x400/120D21/00F5D4?text=Motion+Detection', 'advanced', true, 8),

-- Free Tutorials
('esp32-intro', 'Pengenalan ESP32', 'Tutorial gratis pengenalan ESP32, pin mapping, dan setup development environment.', 12, 'https://youtube.com/watch?v=free1', 'https://placehold.co/600x400/120D21/EAEAEA?text=ESP32+Intro', 'beginner', false, 0),
('arduino-ide-setup', 'Setup Arduino IDE', 'Cara setup Arduino IDE untuk programming ESP32 dengan library yang diperlukan.', 10, 'https://youtube.com/watch?v=free2', 'https://placehold.co/600x400/120D21/EAEAEA?text=Arduino+IDE', 'beginner', false, 0);

-- ==================== KIT-TUTORIAL RELATIONSHIPS ========================
INSERT INTO public.kit_tutorials (kit_id, tutorial_id, is_included_free) VALUES
-- Robot Nolep Kit includes 3 premium tutorials
('robot-nolep', 'dht11-basics', true),
('robot-nolep', 'oled-display', true),
('robot-nolep', 'wifi-control', true),

-- Weather Station Kit includes 2 premium tutorials
('weather-station', 'weather-sensors', true),
('weather-station', 'data-logging', true),

-- IoT Security Kit includes 3 premium tutorials
('iot-security', 'security-basics', true),
('iot-security', 'camera-integration', true),
('iot-security', 'motion-detection', true);

-- ==================== SAMPLE REDEMPTION CODES ========================
INSERT INTO public.redemption_codes (code, kit_id, is_used, notes) VALUES
-- Robot Nolep Kit codes
('ROBOT-NOLEP-ABC123', 'robot-nolep', false, 'Batch Tokopedia #1'),
('ROBOT-NOLEP-DEF456', 'robot-nolep', false, 'Batch Tokopedia #1'),
('ROBOT-NOLEP-GHI789', 'robot-nolep', false, 'Batch Tokopedia #1'),
('ROBOT-NOLEP-JKL012', 'robot-nolep', false, 'Batch Tokopedia #1'),
('ROBOT-NOLEP-MNO345', 'robot-nolep', false, 'Batch Tokopedia #1'),

-- Weather Station Kit codes
('WEATHER-STN-ABC123', 'weather-station', false, 'Batch Tokopedia #1'),
('WEATHER-STN-DEF456', 'weather-station', false, 'Batch Tokopedia #1'),
('WEATHER-STN-GHI789', 'weather-station', false, 'Batch Tokopedia #1'),

-- IoT Security Kit codes
('IOT-SEC-ABC123', 'iot-security', false, 'Batch Tokopedia #1'),
('IOT-SEC-DEF456', 'iot-security', false, 'Batch Tokopedia #1'),
('IOT-SEC-GHI789', 'iot-security', false, 'Batch Tokopedia #1'),

-- Disco Light Kit codes
('DISCO-LIGHT-ABC123', 'disco-light', false, 'Batch Tokopedia #1'),
('DISCO-LIGHT-DEF456', 'disco-light', false, 'Batch Tokopedia #1'),

-- Smart Garden Kit codes
('SMART-GARDEN-ABC123', 'smart-garden', false, 'Batch Tokopedia #1'),
('SMART-GARDEN-DEF456', 'smart-garden', false, 'Batch Tokopedia #1'),

-- LED Matrix Kit codes
('LED-MATRIX-ABC123', 'led-matrix', false, 'Batch Tokopedia #1'),
('LED-MATRIX-DEF456', 'led-matrix', false, 'Batch Tokopedia #1');

-- ==================== SAMPLE USER DATA (for testing) ========================
-- Note: This will be created automatically when users register through auth
-- But we can create some sample profiles for testing

-- Sample admin user profile (you'll need to create this user in Supabase Auth first)
-- INSERT INTO public.user_profiles (id, username, full_name, role) VALUES
-- ('your-admin-user-uuid-here', 'admin', 'Admin User', 'admin');

-- Sample member user profile
-- INSERT INTO public.user_profiles (id, username, full_name, role) VALUES
-- ('your-member-user-uuid-here', 'rebel_01', 'Rebel User', 'member');

-- ==================== SAMPLE USER KIT OWNERSHIP (for testing) ========================
-- Uncomment and update UUIDs after creating test users
-- INSERT INTO public.user_kits (user_id, kit_id, acquired_via) VALUES
-- ('your-member-user-uuid-here', 'robot-nolep', 'code'),
-- ('your-member-user-uuid-here', 'iot-security', 'code');

-- ==================== SAMPLE TUTORIAL ACCESS (for testing) ========================
-- Uncomment and update UUIDs after creating test users
-- INSERT INTO public.user_tutorial_access (user_id, tutorial_id, access_type, kit_id) VALUES
-- ('your-member-user-uuid-here', 'dht11-basics', 'kit', 'robot-nolep'),
-- ('your-member-user-uuid-here', 'oled-display', 'kit', 'robot-nolep'),
-- ('your-member-user-uuid-here', 'wifi-control', 'kit', 'robot-nolep'),
-- ('your-member-user-uuid-here', 'security-basics', 'kit', 'iot-security'),
-- ('your-member-user-uuid-here', 'camera-integration', 'kit', 'iot-security'),
-- ('your-member-user-uuid-here', 'motion-detection', 'kit', 'iot-security');

-- ==================== SAMPLE PROGRESS DATA (for testing) ========================
-- Uncomment and update UUIDs after creating test users
-- INSERT INTO public.user_tutorial_progress (user_id, tutorial_id, progress_percentage, completed_at, watch_time_seconds) VALUES
-- ('your-member-user-uuid-here', 'dht11-basics', 100, NOW(), 900),
-- ('your-member-user-uuid-here', 'oled-display', 65, NULL, 850),
-- ('your-member-user-uuid-here', 'security-basics', 100, NOW(), 1080);

-- ==================== VERIFICATION QUERIES ========================
-- Use these queries to verify the data was inserted correctly

-- Check kits
-- SELECT id, name, price, difficulty_level FROM public.kits WHERE is_active = true;

-- Check tutorials
-- SELECT id, title, duration_minutes, is_premium FROM public.tutorials WHERE is_active = true;

-- Check kit-tutorial relationships
-- SELECT k.name as kit_name, t.title as tutorial_title, kt.is_included_free 
-- FROM public.kit_tutorials kt
-- JOIN public.kits k ON kt.kit_id = k.id
-- JOIN public.tutorials t ON kt.tutorial_id = t.id;

-- Check redemption codes
-- SELECT code, kit_id, is_used, notes FROM public.redemption_codes ORDER BY kit_id, code;
