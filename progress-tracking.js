// ==================== PROGRESS TRACKING SYSTEM ========================
// User progress tracking functions for the IoT Kit Platform
// Created: 2025-08-01

// ==================== PROGRESS STATE ========================

let userProgress = {
    tutorials: {},
    kits: {},
    achievements: []
};

// ==================== PROGRESS FUNCTIONS ========================

// Initialize User Progress
const initializeUserProgress = async () => {
    try {
        // Get current user
        const userResult = getCurrentUserLocal();
        if (!userResult.success || !userResult.user) {
            console.log('No user logged in, skipping progress initialization');
            return;
        }

        // Use Supabase function if available, otherwise fallback to localStorage
        if (typeof getUserDashboardData === 'function') {
            const result = await getUserDashboardData(userResult.user.id);
            if (result.success) {
                userProgress = {
                    tutorials: result.data.tutorials || {},
                    kits: result.data.kits || {},
                    achievements: result.data.achievements || []
                };
            }
        } else {
            // Fallback to localStorage
            const storedProgress = localStorage.getItem(`userProgress_${userResult.user.id}`);
            if (storedProgress) {
                userProgress = JSON.parse(storedProgress);
            }
        }

        // Update dashboard if visible
        updateDashboardProgress();
    } catch (error) {
        console.error('Error initializing user progress:', error);
    }
};

// Update Tutorial Progress
const updateTutorialProgress = async (tutorialId, progress, completed = false) => {
    try {
        const userResult = getCurrentUserLocal();
        if (!userResult.success || !userResult.user) {
            console.log('No user logged in');
            return { success: false, error: 'User not logged in' };
        }

        // Update local state
        userProgress.tutorials[tutorialId] = {
            progress: Math.min(100, Math.max(0, progress)),
            completed: completed || progress >= 100,
            lastAccessed: new Date().toISOString(),
            timeSpent: (userProgress.tutorials[tutorialId]?.timeSpent || 0) + 1
        };

        // Use Supabase function if available
        if (typeof updateTutorialProgressSupabase === 'function') {
            const result = await updateTutorialProgressSupabase(
                userResult.user.id,
                tutorialId,
                userProgress.tutorials[tutorialId]
            );
            
            if (!result.success) {
                console.error('Failed to update progress in Supabase:', result.error);
            }
        } else {
            // Fallback to localStorage
            localStorage.setItem(
                `userProgress_${userResult.user.id}`,
                JSON.stringify(userProgress)
            );
        }

        // Update UI
        updateProgressUI(tutorialId);
        updateDashboardProgress();

        // Check for achievements
        checkAchievements();

        return { success: true };
    } catch (error) {
        console.error('Error updating tutorial progress:', error);
        return { success: false, error: error.message };
    }
};

// Mark Tutorial as Completed
const markTutorialCompleted = async (tutorialId) => {
    return await updateTutorialProgress(tutorialId, 100, true);
};

// Get Tutorial Progress
const getTutorialProgress = (tutorialId) => {
    return userProgress.tutorials[tutorialId] || {
        progress: 0,
        completed: false,
        lastAccessed: null,
        timeSpent: 0
    };
};

// Update Progress UI
const updateProgressUI = (tutorialId) => {
    const progressData = getTutorialProgress(tutorialId);
    
    // Update progress bars
    const progressBars = document.querySelectorAll(`[data-tutorial-id="${tutorialId}"] .progress-bar`);
    progressBars.forEach(bar => {
        const fill = bar.querySelector('.progress-fill');
        if (fill) {
            fill.style.width = `${progressData.progress}%`;
        }
    });

    // Update progress text
    const progressTexts = document.querySelectorAll(`[data-tutorial-id="${tutorialId}"] .progress-text`);
    progressTexts.forEach(text => {
        text.textContent = `${Math.round(progressData.progress)}%`;
    });

    // Update completion status
    const completionBadges = document.querySelectorAll(`[data-tutorial-id="${tutorialId}"] .completion-badge`);
    completionBadges.forEach(badge => {
        if (progressData.completed) {
            badge.classList.remove('hidden');
            badge.innerHTML = '<i data-feather="check-circle" class="w-4 h-4"></i> Selesai';
        } else {
            badge.classList.add('hidden');
        }
    });

    // Re-render feather icons
    if (typeof feather !== 'undefined') {
        feather.replace();
    }
};

// Update Dashboard Progress
const updateDashboardProgress = () => {
    const dashboardStats = calculateDashboardStats();
    
    // Update stats cards
    const totalTutorialsEl = document.getElementById('total-tutorials-stat');
    if (totalTutorialsEl) {
        totalTutorialsEl.textContent = dashboardStats.totalTutorials;
    }

    const completedTutorialsEl = document.getElementById('completed-tutorials-stat');
    if (completedTutorialsEl) {
        completedTutorialsEl.textContent = dashboardStats.completedTutorials;
    }

    const totalKitsEl = document.getElementById('total-kits-stat');
    if (totalKitsEl) {
        totalKitsEl.textContent = dashboardStats.totalKits;
    }

    const achievementsEl = document.getElementById('achievements-stat');
    if (achievementsEl) {
        achievementsEl.textContent = dashboardStats.achievements;
    }

    // Update overall progress bar
    const overallProgressBar = document.getElementById('overall-progress-bar');
    if (overallProgressBar) {
        const fill = overallProgressBar.querySelector('.progress-fill');
        if (fill) {
            fill.style.width = `${dashboardStats.overallProgress}%`;
        }
    }

    const overallProgressText = document.getElementById('overall-progress-text');
    if (overallProgressText) {
        overallProgressText.textContent = `${Math.round(dashboardStats.overallProgress)}%`;
    }
};

// Calculate Dashboard Stats
const calculateDashboardStats = () => {
    const tutorialIds = Object.keys(userProgress.tutorials);
    const completedTutorials = tutorialIds.filter(id => 
        userProgress.tutorials[id].completed
    ).length;

    const totalProgress = tutorialIds.reduce((sum, id) => 
        sum + userProgress.tutorials[id].progress, 0
    );

    const overallProgress = tutorialIds.length > 0 ? totalProgress / tutorialIds.length : 0;

    return {
        totalTutorials: tutorialIds.length,
        completedTutorials,
        totalKits: Object.keys(userProgress.kits).length,
        achievements: userProgress.achievements.length,
        overallProgress
    };
};

// ==================== ACHIEVEMENTS SYSTEM ========================

// Check for Achievements
const checkAchievements = () => {
    const stats = calculateDashboardStats();
    const newAchievements = [];

    // First Tutorial Achievement
    if (stats.completedTutorials >= 1 && !hasAchievement('first-tutorial')) {
        newAchievements.push({
            id: 'first-tutorial',
            title: 'Tutorial Pertama',
            description: 'Menyelesaikan tutorial pertama',
            icon: 'award',
            earnedAt: new Date().toISOString()
        });
    }

    // Tutorial Master Achievement
    if (stats.completedTutorials >= 5 && !hasAchievement('tutorial-master')) {
        newAchievements.push({
            id: 'tutorial-master',
            title: 'Master Tutorial',
            description: 'Menyelesaikan 5 tutorial',
            icon: 'star',
            earnedAt: new Date().toISOString()
        });
    }

    // Perfect Score Achievement
    if (stats.overallProgress >= 100 && !hasAchievement('perfect-score')) {
        newAchievements.push({
            id: 'perfect-score',
            title: 'Skor Sempurna',
            description: 'Menyelesaikan semua tutorial dengan sempurna',
            icon: 'trophy',
            earnedAt: new Date().toISOString()
        });
    }

    // Add new achievements
    if (newAchievements.length > 0) {
        userProgress.achievements.push(...newAchievements);
        showAchievementNotifications(newAchievements);
        saveUserProgress();
    }
};

// Check if user has achievement
const hasAchievement = (achievementId) => {
    return userProgress.achievements.some(achievement => achievement.id === achievementId);
};

// Show Achievement Notifications
const showAchievementNotifications = (achievements) => {
    achievements.forEach((achievement, index) => {
        setTimeout(() => {
            showAchievementModal(achievement);
        }, index * 2000); // Stagger notifications
    });
};

// Show Achievement Modal
const showAchievementModal = (achievement) => {
    // Create achievement modal if it doesn't exist
    let modal = document.getElementById('achievement-modal');
    if (!modal) {
        modal = createAchievementModal();
        document.body.appendChild(modal);
    }

    // Update modal content
    const titleEl = modal.querySelector('.achievement-title');
    const descEl = modal.querySelector('.achievement-description');
    const iconEl = modal.querySelector('.achievement-icon');

    if (titleEl) titleEl.textContent = achievement.title;
    if (descEl) descEl.textContent = achievement.description;
    if (iconEl) iconEl.setAttribute('data-feather', achievement.icon);

    // Show modal
    modal.classList.remove('hidden');
    if (typeof feather !== 'undefined') {
        feather.replace();
    }

    // Auto-hide after 3 seconds
    setTimeout(() => {
        modal.classList.add('hidden');
    }, 3000);
};

// Create Achievement Modal
const createAchievementModal = () => {
    const modal = document.createElement('div');
    modal.id = 'achievement-modal';
    modal.className = 'fixed top-4 right-4 z-50 bg-gradient-to-r from-[var(--primary-accent)] to-[var(--cta-accent)] p-4 rounded-lg shadow-lg max-w-sm hidden';
    modal.innerHTML = `
        <div class="flex items-center space-x-3">
            <div class="achievement-icon w-8 h-8 text-black" data-feather="award"></div>
            <div>
                <h3 class="achievement-title font-bold text-black">Achievement Unlocked!</h3>
                <p class="achievement-description text-black/80 text-sm">Description</p>
            </div>
        </div>
    `;
    return modal;
};

// ==================== SAVE/LOAD FUNCTIONS ========================

// Save User Progress
const saveUserProgress = async () => {
    try {
        const userResult = getCurrentUserLocal();
        if (!userResult.success || !userResult.user) {
            return;
        }

        // Use Supabase function if available
        if (typeof saveUserProgressSupabase === 'function') {
            await saveUserProgressSupabase(userResult.user.id, userProgress);
        } else {
            // Fallback to localStorage
            localStorage.setItem(
                `userProgress_${userResult.user.id}`,
                JSON.stringify(userProgress)
            );
        }
    } catch (error) {
        console.error('Error saving user progress:', error);
    }
};

// ==================== TUTORIAL TAB FUNCTIONS ========================

// Show Tutorial Tab
const showTutorialTab = (tabId) => {
    // Hide all tab contents
    document.querySelectorAll('.tutorial-tab-content').forEach(el => {
        el.classList.remove('active');
    });
    
    // Show selected tab content
    const selectedTab = document.getElementById(tabId);
    if (selectedTab) {
        selectedTab.classList.add('active');
    }

    // Update tab buttons
    document.querySelectorAll('.tutorial-tab').forEach(el => {
        el.classList.remove('border-primary-accent', 'text-primary-accent');
        el.classList.add('border-transparent', 'text-gray-400', 'hover:text-gray-200', 'hover:border-gray-500');
    });
    
    // Find and activate the correct tab button
    const activeTab = document.querySelector(`.tutorial-tab[onclick*="${tabId}"]`);
    if (activeTab) {
        activeTab.classList.add('border-primary-accent', 'text-primary-accent');
        activeTab.classList.remove('border-transparent', 'text-gray-400', 'hover:text-gray-200', 'hover:border-gray-500');
    }
};

// ==================== INITIALIZATION ========================

// Initialize Progress Tracking
const initializeProgressTracking = () => {
    // Initialize user progress
    initializeUserProgress();
    
    // Set up progress tracking for video elements
    const videoElements = document.querySelectorAll('video[data-tutorial-id]');
    videoElements.forEach(video => {
        const tutorialId = video.getAttribute('data-tutorial-id');
        
        video.addEventListener('timeupdate', () => {
            if (video.duration > 0) {
                const progress = (video.currentTime / video.duration) * 100;
                updateTutorialProgress(tutorialId, progress);
            }
        });

        video.addEventListener('ended', () => {
            markTutorialCompleted(tutorialId);
        });
    });
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    initializeProgressTracking();
});
