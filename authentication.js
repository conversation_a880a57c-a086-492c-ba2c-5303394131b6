// ==================== AUTHENTICATION SYSTEM ========================
// Authentication functions for the IoT Kit Platform
// Created: 2025-08-01

// ==================== AUTHENTICATION STATE ========================

let currentUser = null;
let isAuthenticated = false;

// ==================== AUTHENTICATION FUNCTIONS ========================

// Sign Up Function
const signUp = async (event) => {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);
    const email = formData.get('email');
    const password = formData.get('password');
    const confirmPassword = formData.get('confirm-password');
    const username = formData.get('username');
    
    // Validation
    if (password !== confirmPassword) {
        alert('Password tidak cocok!');
        return;
    }
    
    if (password.length < 6) {
        alert('Password minimal 6 karakter!');
        return;
    }
    
    try {
        // Use Supabase function if available, otherwise fallback to mock
        let result;
        if (typeof signUpUser === 'function') {
            result = await signUpUser(email, password, username);
        } else {
            // Mock registration for development
            result = await mockSignUp(email, password, username);
        }
        
        if (result.success) {
            alert('Registrasi berhasil! Silakan login.');
            showPage('login-page');
        } else {
            alert(`Registrasi gagal: ${result.error}`);
        }
    } catch (error) {
        console.error('Registration error:', error);
        alert('Terjadi kesalahan saat registrasi. Silakan coba lagi.');
    }
};

// Sign In Function
const signIn = async (event) => {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);
    const email = formData.get('email');
    const password = formData.get('password');
    
    try {
        // Use Supabase function if available, otherwise fallback to mock
        let result;
        if (typeof signInUser === 'function') {
            result = await signInUser(email, password);
        } else {
            // Mock login for development
            result = await mockSignIn(email, password);
        }
        
        if (result.success) {
            currentUser = result.user;
            isAuthenticated = true;
            
            // Update UI for authenticated user
            updateUIForAuthenticatedUser();
            
            // Redirect to dashboard
            showPage('member-area-page');
            
            // Show success message
            alert('Login berhasil! Selamat datang.');
        } else {
            alert(`Login gagal: ${result.error}`);
        }
    } catch (error) {
        console.error('Login error:', error);
        alert('Terjadi kesalahan saat login. Silakan coba lagi.');
    }
};

// Sign Out Function
const signOut = async () => {
    try {
        // Use Supabase function if available, otherwise fallback to mock
        let result;
        if (typeof signOutUser === 'function') {
            result = await signOutUser();
        } else {
            // Mock logout for development
            result = { success: true };
        }
        
        if (result.success) {
            currentUser = null;
            isAuthenticated = false;
            
            // Update UI for unauthenticated user
            updateUIForUnauthenticatedUser();
            
            // Redirect to landing page
            showPage('landing-page');
            
            // Show success message
            alert('Logout berhasil. Sampai jumpa!');
        }
    } catch (error) {
        console.error('Logout error:', error);
        alert('Terjadi kesalahan saat logout.');
    }
};

// Forgot Password Function
const forgotPassword = async (event) => {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);
    const email = formData.get('email');
    
    try {
        // Use Supabase function if available, otherwise fallback to mock
        let result;
        if (typeof resetPassword === 'function') {
            result = await resetPassword(email);
        } else {
            // Mock reset for development
            result = { success: true };
        }
        
        if (result.success) {
            alert('Link reset password telah dikirim ke email Anda.');
            showPage('login-page');
        } else {
            alert(`Reset password gagal: ${result.error}`);
        }
    } catch (error) {
        console.error('Reset password error:', error);
        alert('Terjadi kesalahan. Silakan coba lagi.');
    }
};

// ==================== MOCK AUTHENTICATION FUNCTIONS ========================

// Mock Sign Up (for development)
const mockSignUp = async (email, password, username) => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Check if user already exists
    const existingUsers = JSON.parse(localStorage.getItem('mockUsers') || '[]');
    const userExists = existingUsers.find(user => user.email === email);
    
    if (userExists) {
        return { success: false, error: 'Email sudah terdaftar!' };
    }
    
    // Create new user
    const newUser = {
        id: Date.now().toString(),
        email,
        username,
        password, // In real app, this would be hashed
        created_at: new Date().toISOString()
    };
    
    existingUsers.push(newUser);
    localStorage.setItem('mockUsers', JSON.stringify(existingUsers));
    
    return { success: true, user: newUser };
};

// Mock Sign In (for development)
const mockSignIn = async (email, password) => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Check credentials
    const existingUsers = JSON.parse(localStorage.getItem('mockUsers') || '[]');
    const user = existingUsers.find(u => u.email === email && u.password === password);
    
    if (!user) {
        return { success: false, error: 'Email atau password salah!' };
    }
    
    // Store session
    localStorage.setItem('currentUser', JSON.stringify(user));
    
    return { success: true, user };
};

// ==================== UI UPDATE FUNCTIONS ========================

// Update UI for authenticated user
const updateUIForAuthenticatedUser = () => {
    // Hide login/register buttons
    const authButtons = document.querySelectorAll('.auth-button');
    authButtons.forEach(button => {
        button.style.display = 'none';
    });
    
    // Show user menu
    const userMenu = document.getElementById('user-menu');
    if (userMenu) {
        userMenu.style.display = 'block';
    }
    
    // Update user info
    const userNameElement = document.getElementById('user-name');
    if (userNameElement && currentUser) {
        userNameElement.textContent = currentUser.username || currentUser.email;
    }
    
    // Show member area in navigation
    const memberAreaNav = document.getElementById('member-area-nav');
    if (memberAreaNav) {
        memberAreaNav.style.display = 'block';
    }
};

// Update UI for unauthenticated user
const updateUIForUnauthenticatedUser = () => {
    // Show login/register buttons
    const authButtons = document.querySelectorAll('.auth-button');
    authButtons.forEach(button => {
        button.style.display = 'block';
    });
    
    // Hide user menu
    const userMenu = document.getElementById('user-menu');
    if (userMenu) {
        userMenu.style.display = 'none';
    }
    
    // Hide member area in navigation
    const memberAreaNav = document.getElementById('member-area-nav');
    if (memberAreaNav) {
        memberAreaNav.style.display = 'none';
    }
    
    // Clear user data
    localStorage.removeItem('currentUser');
};

// ==================== AUTHENTICATION CHECKS ========================

// Check if user is authenticated
const checkAuthentication = () => {
    return isAuthenticated && currentUser !== null;
};

// Require authentication for protected pages
const requireAuth = (callback) => {
    if (!checkAuthentication()) {
        alert('Silakan login terlebih dahulu!');
        showPage('login-page');
        return false;
    }
    
    if (callback && typeof callback === 'function') {
        callback();
    }
    
    return true;
};

// Get current user
const getCurrentUserLocal = () => {
    if (currentUser) {
        return { success: true, user: currentUser };
    }
    
    // Try to get from localStorage
    const storedUser = localStorage.getItem('currentUser');
    if (storedUser) {
        try {
            currentUser = JSON.parse(storedUser);
            isAuthenticated = true;
            return { success: true, user: currentUser };
        } catch (error) {
            console.error('Error parsing stored user:', error);
        }
    }
    
    return { success: false, user: null };
};

// ==================== INITIALIZATION ========================

// Initialize authentication state
const initializeAuth = async () => {
    try {
        // Check if user is already logged in (Supabase)
        if (typeof getCurrentUser === 'function') {
            const result = await getCurrentUser();
            if (result.success && result.user) {
                currentUser = result.user;
                isAuthenticated = true;
                updateUIForAuthenticatedUser();
                return;
            }
        }
        
        // Fallback to localStorage check
        const localResult = getCurrentUserLocal();
        if (localResult.success) {
            updateUIForAuthenticatedUser();
        } else {
            updateUIForUnauthenticatedUser();
        }
    } catch (error) {
        console.error('Auth initialization error:', error);
        updateUIForUnauthenticatedUser();
    }
};

// ==================== FORM SETUP ========================

// Setup authentication forms
const setupAuthForms = () => {
    // Setup login form
    const loginForm = document.getElementById('login-form');
    if (loginForm) {
        loginForm.addEventListener('submit', signIn);
    }
    
    // Setup register form
    const registerForm = document.getElementById('register-form');
    if (registerForm) {
        registerForm.addEventListener('submit', signUp);
    }
    
    // Setup forgot password form
    const forgotForm = document.getElementById('forgot-form');
    if (forgotForm) {
        forgotForm.addEventListener('submit', forgotPassword);
    }
    
    // Setup logout buttons
    const logoutButtons = document.querySelectorAll('.logout-button');
    logoutButtons.forEach(button => {
        button.addEventListener('click', signOut);
    });
};

// Initialize authentication when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    setupAuthForms();
    initializeAuth();
});
