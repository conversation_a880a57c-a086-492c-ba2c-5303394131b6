// ==================== TECHNO-REBEL IOT KIT PLATFORM - MAIN APPLICATION ========================
// Main JavaScript application for the IoT Kit Platform
// Created: 2025-08-01

// ==================== GLOBAL VARIABLES ========================
let currentLanguage = 'id';
let currentUser = null;

// ==================== CORE NAVIGATION FUNCTIONS ========================

// Show specific page and hide others
const showPage = (pageId) => {
    // Hide all pages
    document.querySelectorAll('.page').forEach(page => {
        page.style.display = 'none';
    });
    
    // Show target page
    const targetPage = document.getElementById(pageId);
    if (targetPage) {
        targetPage.style.display = 'block';
        
        // Update navigation state
        updateNavigationState(pageId);
        
        // Page-specific initialization
        initializePage(pageId);
    }
};

// Update navigation state based on current page
const updateNavigationState = (pageId) => {
    // Update active nav links
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });
    
    // Add active class to current page nav link
    const activeLink = document.querySelector(`[onclick="showPage('${pageId}')"]`);
    if (activeLink) {
        activeLink.classList.add('active');
    }
    
    // Update page title
    updatePageTitle(pageId);
};

// Initialize page-specific functionality
const initializePage = (pageId) => {
    switch (pageId) {
        case 'member-area-page':
            initializeDashboard();
            break;
        case 'admin-page':
            initializeAdminDashboard();
            break;
        case 'projects-page':
            initializeProjectsPage();
            break;
        case 'tutorials-page':
            initializeTutorialsPage();
            break;
        default:
            break;
    }
    
    // Refresh Feather icons
    if (typeof feather !== 'undefined') {
        feather.replace();
    }
};

// Update page title based on current page
const updatePageTitle = (pageId) => {
    const titles = {
        'landing-page': 'Techno-Rebel - IoT Kit Platform',
        'projects-page': 'Projects - Techno-Rebel',
        'tutorials-page': 'Tutorials - Techno-Rebel',
        'member-area-page': 'Dashboard - Techno-Rebel',
        'admin-page': 'Admin Dashboard - Techno-Rebel',
        'login-page': 'Login - Techno-Rebel',
        'register-page': 'Register - Techno-Rebel'
    };
    
    document.title = titles[pageId] || 'Techno-Rebel';
};

// ==================== LANGUAGE SWITCHING ========================

// Toggle between Indonesian and English
const toggleLanguage = () => {
    currentLanguage = currentLanguage === 'id' ? 'en' : 'id';
    updateLanguageDisplay();
    
    // Save preference
    localStorage.setItem('preferred-language', currentLanguage);
};

// Update all text elements based on current language
const updateLanguageDisplay = () => {
    document.querySelectorAll('[data-lang]').forEach(element => {
        const langData = element.getAttribute('data-lang');
        try {
            const translations = JSON.parse(langData);
            if (translations[currentLanguage]) {
                element.textContent = translations[currentLanguage];
            }
        } catch (e) {
            console.warn('Invalid language data:', langData);
        }
    });
    
    // Update language toggle button
    const langToggle = document.getElementById('lang-toggle');
    if (langToggle) {
        langToggle.textContent = currentLanguage === 'id' ? 'EN' : 'ID';
    }
};

// Initialize language from localStorage
const initializeLanguage = () => {
    const savedLang = localStorage.getItem('preferred-language');
    if (savedLang && ['id', 'en'].includes(savedLang)) {
        currentLanguage = savedLang;
    }
    updateLanguageDisplay();
};

// ==================== MODAL MANAGEMENT ========================

// Open modal with animation
const openModal = (modalId) => {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.add('active');
        modal.style.display = 'flex';
        
        // Focus first input if available
        const firstInput = modal.querySelector('input, select, textarea');
        if (firstInput) {
            setTimeout(() => firstInput.focus(), 100);
        }
        
        // Add escape key listener
        document.addEventListener('keydown', handleModalEscape);
    }
};

// Close modal with animation
const closeModal = (modalId) => {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('active');
        
        // Clear any validation messages
        const resultDiv = modal.querySelector('#code-validation-result');
        if (resultDiv) {
            resultDiv.classList.add('hidden');
        }
        
        // Reset forms
        const forms = modal.querySelectorAll('form');
        forms.forEach(form => form.reset());
        
        // Remove escape key listener
        document.removeEventListener('keydown', handleModalEscape);
    }
};

// Handle escape key to close modals
const handleModalEscape = (event) => {
    if (event.key === 'Escape') {
        const activeModal = document.querySelector('.modal-container.active');
        if (activeModal) {
            closeModal(activeModal.id);
        }
    }
};

// ==================== FILTER AND SEARCH FUNCTIONS ========================

// Filter projects by category
const filterProjects = (category) => {
    const projects = document.querySelectorAll('.project-card');
    const filterButtons = document.querySelectorAll('.filter-btn');
    
    // Update active filter button
    filterButtons.forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');
    
    // Show/hide projects
    projects.forEach(project => {
        if (category === 'all' || project.dataset.category === category) {
            project.style.display = 'block';
            project.classList.add('fade-in');
        } else {
            project.style.display = 'none';
            project.classList.remove('fade-in');
        }
    });
};

// Search projects by title or description
const searchProjects = (query) => {
    const projects = document.querySelectorAll('.project-card');
    const searchTerm = query.toLowerCase();
    
    projects.forEach(project => {
        const title = project.querySelector('h3').textContent.toLowerCase();
        const description = project.querySelector('p').textContent.toLowerCase();
        
        if (title.includes(searchTerm) || description.includes(searchTerm)) {
            project.style.display = 'block';
        } else {
            project.style.display = 'none';
        }
    });
};

// Filter tutorials by difficulty
const filterTutorials = (difficulty) => {
    const tutorials = document.querySelectorAll('.tutorial-card');
    const filterButtons = document.querySelectorAll('.tutorial-filter-btn');
    
    // Update active filter button
    filterButtons.forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');
    
    // Show/hide tutorials
    tutorials.forEach(tutorial => {
        if (difficulty === 'all' || tutorial.dataset.difficulty === difficulty) {
            tutorial.style.display = 'block';
        } else {
            tutorial.style.display = 'none';
        }
    });
};

// ==================== TUTORIAL TAB MANAGEMENT ========================

// Show specific tutorial tab content
const showTutorialTab = (tabId) => {
    // Hide all tab contents
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });
    
    // Remove active class from all tabs
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    // Show target tab content
    const targetContent = document.getElementById(tabId);
    if (targetContent) {
        targetContent.classList.add('active');
    }
    
    // Add active class to clicked tab
    const activeTab = document.querySelector(`[onclick="showTutorialTab('${tabId}')"]`);
    if (activeTab) {
        activeTab.classList.add('active');
    }
};

// ==================== UTILITY FUNCTIONS ========================

// Format currency to Indonesian Rupiah
const formatCurrency = (amount) => {
    return new Intl.NumberFormat('id-ID', {
        style: 'currency',
        currency: 'IDR',
        minimumFractionDigits: 0
    }).format(amount);
};

// Format date to Indonesian format
const formatDate = (date) => {
    return new Intl.DateTimeFormat('id-ID', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    }).format(new Date(date));
};

// Debounce function for search inputs
const debounce = (func, wait) => {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
};

// Show loading state
const showLoading = (element, text = 'Loading...') => {
    if (element) {
        element.innerHTML = `<i data-feather="loader" class="w-4 h-4 animate-spin mr-2"></i>${text}`;
        element.disabled = true;
        feather.replace();
    }
};

// Hide loading state
const hideLoading = (element, originalText) => {
    if (element) {
        element.innerHTML = originalText;
        element.disabled = false;
        feather.replace();
    }
};

// ==================== INITIALIZATION ========================

// Initialize application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Initialize language
    initializeLanguage();
    
    // Show landing page by default
    showPage('landing-page');
    
    // Initialize Feather icons
    if (typeof feather !== 'undefined') {
        feather.replace();
    }
    
    // Setup search debouncing
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
        searchInput.addEventListener('input', debounce((e) => {
            searchProjects(e.target.value);
        }, 300));
    }
    
    // Setup mobile menu toggle
    setupMobileMenu();
    
    // Check authentication state
    checkAuthenticationState();
    
    console.log('Techno-Rebel IoT Kit Platform initialized successfully');
});

// Setup mobile menu functionality
const setupMobileMenu = () => {
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const mobileMenu = document.getElementById('mobile-menu');
    
    if (mobileMenuBtn && mobileMenu) {
        mobileMenuBtn.addEventListener('click', () => {
            mobileMenu.classList.toggle('active');
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', (e) => {
            if (!mobileMenu.contains(e.target) && !mobileMenuBtn.contains(e.target)) {
                mobileMenu.classList.remove('active');
            }
        });
    }
};

// Check authentication state on page load
const checkAuthenticationState = async () => {
    if (typeof getCurrentUser === 'function') {
        try {
            const result = await getCurrentUser();
            if (result.success && result.user) {
                currentUser = result.user;
                updateUIForAuthenticatedUser();
            }
        } catch (error) {
            console.log('No authenticated user found');
        }
    }
};

// Update UI for authenticated user
const updateUIForAuthenticatedUser = () => {
    // Update navigation to show member area
    // Hide login/register buttons
    // Show user profile info
    console.log('User authenticated:', currentUser);
};
